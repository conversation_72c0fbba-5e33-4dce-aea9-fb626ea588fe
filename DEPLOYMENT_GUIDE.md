# 🚀 دليل رفع موقع شركة بريق ضوء الشمس على الدومين

## 📋 قائمة فحص الجاهزية

### ✅ الملفات الجاهزة للرفع
- [x] جميع ملفات PHP
- [x] ملفات CSS و JavaScript
- [x] ملف قاعدة البيانات (database.sql)
- [x] ملف .htaccess للأمان
- [x] مجلدات الصور منظمة
- [x] لوحة الإدارة كاملة

## 🔧 متطلبات الخادم

### الحد الأدنى:
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث
- **Apache**: مع mod_rewrite
- **مساحة**: 500 ميجابايت
- **الذاكرة**: 256 ميجابايت

### المستحسن:
- **PHP**: 8.0+
- **MySQL**: 8.0+
- **مساحة**: 1 جيجابايت
- **الذاكرة**: 512 ميجابايت

## 📁 خطوات الرفع

### 1. رفع الملفات
```bash
# ارفع جميع الملفات إلى المجلد الرئيسي
# عادة: public_html أو www أو httpdocs
```

### 2. إعداد قاعدة البيانات
1. **إنشاء قاعدة بيانات** في لوحة تحكم الاستضافة
2. **استيراد database.sql**
3. **تحديث إعدادات الاتصال**

### 3. تحديث ملف الاتصال
```php
// في includes/db_connect.php
$host = 'localhost'; // أو عنوان الخادم
$dbname = 'your_database_name';
$username = 'your_db_username';
$password = 'your_db_password';
```

## 🔐 الصلاحيات المطلوبة

### صلاحيات المجلدات (755):
```bash
chmod 755 uploads/
chmod 755 uploads/products_images/
chmod 755 uploads/packages_images/
chmod 755 uploads/media/
chmod 755 uploads/sliders/
chmod 755 uploads/team/
chmod 755 uploads/certificates/
chmod 755 uploads/logo/
chmod 755 admin/
```

### صلاحيات الملفات (644):
```bash
chmod 644 *.php
chmod 644 admin/*.php
chmod 644 includes/*.php
chmod 644 .htaccess
```

### أوامر سريعة:
```bash
# تطبيق الصلاحيات على جميع المجلدات
find . -type d -exec chmod 755 {} \;

# تطبيق الصلاحيات على جميع الملفات
find . -type f -exec chmod 644 {} \;
```

## 🛡️ إعدادات الأمان

### 1. تغيير كلمة مرور الإدارة
- **اسم المستخدم الافتراضي**: admin
- **كلمة المرور الافتراضية**: admin123
- ⚠️ **يجب تغييرها فوراً بعد الرفع!**

### 2. حماية الملفات الحساسة
- ✅ ملف .htaccess يحمي الملفات الحساسة
- ✅ منع الوصول لملفات قاعدة البيانات
- ✅ حماية مجلد admin
- ✅ إخفاء معلومات الخادم

### 3. إعدادات SSL (HTTPS)
```apache
# في .htaccess - إعادة توجيه إلى HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 📂 هيكل المجلدات النهائي

```
your-domain.com/
├── admin/                  # لوحة الإدارة
│   ├── assets/            # ملفات الإدارة
│   ├── includes/          # ملفات مشتركة للإدارة
│   └── *.php             # صفحات الإدارة
├── assets/                # ملفات الموقع الرئيسي
│   ├── css/              # ملفات التنسيق
│   ├── js/               # ملفات JavaScript
│   └── img/              # الصور الثابتة
├── includes/              # ملفات PHP المشتركة
│   ├── db_connect.php    # اتصال قاعدة البيانات
│   ├── functions.php     # الدوال المساعدة
│   ├── header.php        # رأس الصفحة
│   └── footer.php        # تذييل الصفحة
├── uploads/               # الملفات المرفوعة
│   ├── products_images/   # صور المنتجات
│   ├── packages_images/   # صور البكجات
│   ├── media/            # معرض الأعمال
│   ├── sliders/          # صور السلايدر
│   ├── team/             # صور الفريق
│   ├── certificates/     # الشهادات
│   └── logo/             # شعار الشركة
├── *.php                 # صفحات الموقع الرئيسية
├── .htaccess            # إعدادات الخادم
├── database.sql         # ملف قاعدة البيانات
└── config.example.php   # مثال ملف الإعدادات
```

## 🔍 اختبار الموقع بعد الرفع

### اختبارات أساسية:
- [ ] تحميل الصفحة الرئيسية
- [ ] عرض المنتجات والبكجات
- [ ] عمل البحث والفلترة
- [ ] تسجيل دخول الإدارة
- [ ] رفع الصور في الإدارة
- [ ] عمل نماذج التواصل
- [ ] روابط الواتساب
- [ ] عرض المعرض
- [ ] صفحة الفريق

### اختبارات الأمان:
- [ ] عدم إمكانية الوصول لملفات .sql
- [ ] حماية مجلد admin
- [ ] عمل HTTPS
- [ ] إخفاء معلومات الخادم

## ⚡ تحسينات الأداء

### مفعلة تلقائياً:
- ✅ ضغط الملفات (Gzip)
- ✅ تخزين مؤقت للمتصفح
- ✅ تحسين الصور
- ✅ تصغير CSS

### يمكن إضافتها:
- CDN للصور
- تحسين قاعدة البيانات
- تخزين مؤقت متقدم

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ اتصال قاعدة البيانات
```
الحل: تحقق من إعدادات db_connect.php
```

#### 2. صور لا تظهر
```
الحل: تحقق من صلاحيات مجلد uploads (755)
```

#### 3. صفحة 404 للإدارة
```
الحل: تأكد من رفع مجلد admin كاملاً
```

#### 4. خطأ في رفع الملفات
```
الحل: تحقق من صلاحيات الكتابة وحجم الملف المسموح
```

## 📞 معلومات الدعم

### سجلات الأخطاء:
- تحقق من error_log في لوحة تحكم الاستضافة
- فعل عرض الأخطاء مؤقتاً للتشخيص

### نصائح مهمة:
1. احتفظ بنسخة احتياطية قبل أي تعديل
2. اختبر الموقع على subdomain أولاً
3. استخدم HTTPS دائماً
4. راقب استهلاك الموارد
5. حدث كلمات المرور بانتظام

---

## ✅ الموقع جاهز للرفع!

جميع الملفات منظمة ومحسنة للأداء والأمان. اتبع الخطوات أعلاه وستحصل على موقع احترافي وآمن.

**نصيحة أخيرة**: اختبر جميع الوظائف بعد الرفع للتأكد من عمل كل شيء بشكل صحيح.
