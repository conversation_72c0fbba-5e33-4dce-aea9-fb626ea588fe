<?php
$page_title = 'إدارة إنجازات الفريق';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التحقق من وجود الجداول وإنشاؤها إذا لم تكن موجودة
try {
    // التحقق من وجود جدول team_achievements
    $check_table = $pdo->query("SHOW TABLES LIKE 'team_achievements'");
    if ($check_table->rowCount() == 0) {
        // قراءة وتنفيذ ملف SQL
        $sql_file = file_get_contents('create_team_achievements_table.sql');
        $pdo->exec($sql_file);
        $_SESSION['success_message'] = "تم إنشاء جداول إنجازات الفريق بنجاح";
    }
} catch (Exception $e) {
    $_SESSION['error_message'] = "خطأ في إنشاء الجداول: " . $e->getMessage();
}

// معالجة النماذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_achievement':
                $type = clean_input($_POST['type']);
                $title = clean_input($_POST['title']);
                $value = clean_input($_POST['value']);
                $description = clean_input($_POST['description']);
                $icon = clean_input($_POST['icon']);
                $percentage = !empty($_POST['percentage']) ? (int)$_POST['percentage'] : null;
                $color = clean_input($_POST['color']);
                $sort_order = (int)$_POST['sort_order'];

                $stmt = $pdo->prepare("INSERT INTO team_achievements (type, title, value, description, icon, percentage, color, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$type, $title, $value, $description, $icon, $percentage, $color, $sort_order])) {
                    $_SESSION['success_message'] = "تم إضافة الإنجاز بنجاح";
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء إضافة الإنجاز";
                }
                break;

            case 'edit_achievement':
                $id = (int)$_POST['id'];
                $type = clean_input($_POST['type']);
                $title = clean_input($_POST['title']);
                $value = clean_input($_POST['value']);
                $description = clean_input($_POST['description']);
                $icon = clean_input($_POST['icon']);
                $percentage = !empty($_POST['percentage']) ? (int)$_POST['percentage'] : null;
                $color = clean_input($_POST['color']);
                $sort_order = (int)$_POST['sort_order'];

                $stmt = $pdo->prepare("UPDATE team_achievements SET type=?, title=?, value=?, description=?, icon=?, percentage=?, color=?, sort_order=? WHERE id=?");
                if ($stmt->execute([$type, $title, $value, $description, $icon, $percentage, $color, $sort_order, $id])) {
                    $_SESSION['success_message'] = "تم تحديث الإنجاز بنجاح";
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء تحديث الإنجاز";
                }
                break;

            case 'delete_achievement':
                $id = (int)$_POST['id'];
                $stmt = $pdo->prepare("DELETE FROM team_achievements WHERE id=?");
                if ($stmt->execute([$id])) {
                    $_SESSION['success_message'] = "تم حذف الإنجاز بنجاح";
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء حذف الإنجاز";
                }
                break;

            case 'toggle_status':
                $id = (int)$_POST['id'];
                $stmt = $pdo->prepare("UPDATE team_achievements SET status = 1 - status WHERE id=?");
                if ($stmt->execute([$id])) {
                    $_SESSION['success_message'] = "تم تغيير حالة الإنجاز بنجاح";
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء تغيير حالة الإنجاز";
                }
                break;

            case 'update_section_settings':
                $section_name = clean_input($_POST['section_name']);
                $title = clean_input($_POST['title']);
                $subtitle = clean_input($_POST['subtitle']);
                $description = clean_input($_POST['description']);
                $main_text = clean_input($_POST['main_text']);
                $secondary_text = clean_input($_POST['secondary_text']);

                $stmt = $pdo->prepare("UPDATE team_section_settings SET title=?, subtitle=?, description=?, main_text=?, secondary_text=? WHERE section_name=?");
                if ($stmt->execute([$title, $subtitle, $description, $main_text, $secondary_text, $section_name])) {
                    $_SESSION['success_message'] = "تم تحديث إعدادات القسم بنجاح";
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء تحديث إعدادات القسم";
                }
                break;
        }

        header('Location: team-achievements.php');
        exit;
    }
}

// جلب البيانات
try {
    $achievements_stmt = $pdo->query("SELECT * FROM team_achievements ORDER BY type, sort_order");
    $achievements = $achievements_stmt->fetchAll(PDO::FETCH_ASSOC);

    $settings_stmt = $pdo->query("SELECT * FROM team_section_settings");
    $settings = $settings_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم الإعدادات
    $section_settings = [];
    foreach ($settings as $setting) {
        $section_settings[$setting['section_name']] = $setting;
    }
} catch (Exception $e) {
    $achievements = [];
    $section_settings = [];
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة إنجازات الفريق</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addAchievementModal">
            <i class="fas fa-plus me-1"></i> إضافة إنجاز جديد
        </button>
    </div>
</div>

<?php if (isset($_SESSION['success_message'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إعدادات الأقسام -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">إعدادات أقسام الفريق</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- إعدادات قسم الإنجازات -->
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">قسم خبرة وكفاءة</h6>
                        <form method="POST">
                            <input type="hidden" name="action" value="update_section_settings">
                            <input type="hidden" name="section_name" value="achievements">

                            <div class="mb-3">
                                <label class="form-label">العنوان الرئيسي</label>
                                <input type="text" class="form-control" name="title" value="<?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['title'] : 'خبرة وكفاءة'; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان الفرعي</label>
                                <input type="text" class="form-control" name="subtitle" value="<?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['subtitle'] : 'إنجازاتنا'; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3" required><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['description'] : 'نفخر بإنجازاتنا وخبراتنا المتراكمة في مجال الطاقة الشمسية'; ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">النص الرئيسي</label>
                                <input type="text" class="form-control" name="main_text" value="<?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['main_text'] : 'فريق متكامل من الخبراء'; ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">النص الثانوي</label>
                                <textarea class="form-control" name="secondary_text" rows="3"><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['secondary_text'] : 'يتكون فريقنا من مهندسين متخصصين في تصميم أنظمة الطاقة الشمسية وفنيين محترفين في تركيب وصيانة هذه الأنظمة.'; ?></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </form>
                    </div>

                    <!-- إعدادات وصف الخبرات -->
                    <div class="col-md-6">
                        <h6 class="text-success mb-3">وصف الخبرات</h6>
                        <form method="POST">
                            <input type="hidden" name="action" value="update_section_settings">
                            <input type="hidden" name="section_name" value="expertise_description">

                            <div class="mb-3">
                                <label class="form-label">النص الوصفي للخبرات</label>
                                <textarea class="form-control" name="description" rows="6" required><?php echo isset($section_settings['expertise_description']) ? $section_settings['expertise_description']['description'] : 'نحرص على تدريب فريقنا باستمرار على أحدث التقنيات والمعايير العالمية لضمان تقديم أفضل الخدمات لعملائنا. يمتلك فريقنا خبرة تزيد عن 10 سنوات في مجال الطاقة الشمسية.'; ?></textarea>
                            </div>

                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الإنجازات -->
<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">قائمة الإنجازات والخبرات</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>النوع</th>
                        <th>العنوان</th>
                        <th>القيمة/النسبة</th>
                        <th>الأيقونة</th>
                        <th>الترتيب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($achievements)): ?>
                    <tr>
                        <td colspan="7" class="text-center">لا توجد إنجازات</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($achievements as $achievement): ?>
                        <tr>
                            <td>
                                <span class="badge <?php echo $achievement['type'] == 'achievement' ? 'bg-primary' : 'bg-success'; ?>">
                                    <?php echo $achievement['type'] == 'achievement' ? 'إنجاز' : 'خبرة'; ?>
                                </span>
                            </td>
                            <td><?php echo $achievement['title']; ?></td>
                            <td>
                                <?php if ($achievement['type'] == 'achievement'): ?>
                                    <strong class="text-primary"><?php echo $achievement['value']; ?></strong>
                                <?php else: ?>
                                    <div class="progress" style="width: 100px;">
                                        <div class="progress-bar bg-<?php echo $achievement['color']; ?>" style="width: <?php echo $achievement['percentage']; ?>%"></div>
                                    </div>
                                    <small><?php echo $achievement['percentage']; ?>%</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <i class="<?php echo $achievement['icon']; ?> text-<?php echo $achievement['color']; ?>"></i>
                            </td>
                            <td><?php echo $achievement['sort_order']; ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="id" value="<?php echo $achievement['id']; ?>">
                                    <button type="submit" class="btn btn-sm <?php echo $achievement['status'] ? 'btn-success' : 'btn-secondary'; ?>">
                                        <?php echo $achievement['status'] ? 'نشط' : 'غير نشط'; ?>
                                    </button>
                                </form>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-info" onclick="editAchievement(<?php echo htmlspecialchars(json_encode($achievement)); ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإنجاز؟')">
                                    <input type="hidden" name="action" value="delete_achievement">
                                    <input type="hidden" name="id" value="<?php echo $achievement['id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نافذة إضافة إنجاز جديد -->
<div class="modal fade" id="addAchievementModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة إنجاز جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_achievement">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الإنجاز</label>
                        <select class="form-select" name="type" id="achievementType" onchange="toggleFields()" required>
                            <option value="achievement">إنجاز</option>
                            <option value="expertise">خبرة</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>

                    <div class="mb-3" id="valueField">
                        <label class="form-label">القيمة (للإنجازات)</label>
                        <input type="text" class="form-control" name="value" placeholder="مثل: +500">
                    </div>

                    <div class="mb-3" id="percentageField" style="display: none;">
                        <label class="form-label">النسبة المئوية (للخبرات)</label>
                        <input type="number" class="form-control" name="percentage" min="0" max="100" placeholder="95">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأيقونة (Font Awesome)</label>
                        <input type="text" class="form-control" name="icon" placeholder="fas fa-project-diagram" required>
                        <small class="form-text text-muted">مثال: fas fa-users, fas fa-award, fas fa-chart-bar</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اللون</label>
                        <select class="form-select" name="color" required>
                            <option value="primary">أزرق (Primary)</option>
                            <option value="success">أخضر (Success)</option>
                            <option value="info">سماوي (Info)</option>
                            <option value="warning">أصفر (Warning)</option>
                            <option value="danger">أحمر (Danger)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" name="sort_order" value="1" min="1" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل إنجاز -->
<div class="modal fade" id="editAchievementModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الإنجاز</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="edit_achievement">
                <input type="hidden" name="id" id="editId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الإنجاز</label>
                        <select class="form-select" name="type" id="editType" onchange="toggleEditFields()" required>
                            <option value="achievement">إنجاز</option>
                            <option value="expertise">خبرة</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <input type="text" class="form-control" name="title" id="editTitle" required>
                    </div>

                    <div class="mb-3" id="editValueField">
                        <label class="form-label">القيمة (للإنجازات)</label>
                        <input type="text" class="form-control" name="value" id="editValue" placeholder="مثل: +500">
                    </div>

                    <div class="mb-3" id="editPercentageField" style="display: none;">
                        <label class="form-label">النسبة المئوية (للخبرات)</label>
                        <input type="number" class="form-control" name="percentage" id="editPercentage" min="0" max="100" placeholder="95">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" id="editDescription" rows="3" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأيقونة (Font Awesome)</label>
                        <input type="text" class="form-control" name="icon" id="editIcon" placeholder="fas fa-project-diagram" required>
                        <small class="form-text text-muted">مثال: fas fa-users, fas fa-award, fas fa-chart-bar</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اللون</label>
                        <select class="form-select" name="color" id="editColor" required>
                            <option value="primary">أزرق (Primary)</option>
                            <option value="success">أخضر (Success)</option>
                            <option value="info">سماوي (Info)</option>
                            <option value="warning">أصفر (Warning)</option>
                            <option value="danger">أحمر (Danger)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" name="sort_order" id="editSortOrder" value="1" min="1" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleFields() {
    const type = document.getElementById('achievementType').value;
    const valueField = document.getElementById('valueField');
    const percentageField = document.getElementById('percentageField');

    if (type === 'achievement') {
        valueField.style.display = 'block';
        percentageField.style.display = 'none';
        document.querySelector('#valueField input').required = true;
        document.querySelector('#percentageField input').required = false;
    } else {
        valueField.style.display = 'none';
        percentageField.style.display = 'block';
        document.querySelector('#valueField input').required = false;
        document.querySelector('#percentageField input').required = true;
    }
}

function toggleEditFields() {
    const type = document.getElementById('editType').value;
    const valueField = document.getElementById('editValueField');
    const percentageField = document.getElementById('editPercentageField');

    if (type === 'achievement') {
        valueField.style.display = 'block';
        percentageField.style.display = 'none';
        document.querySelector('#editValueField input').required = true;
        document.querySelector('#editPercentageField input').required = false;
    } else {
        valueField.style.display = 'none';
        percentageField.style.display = 'block';
        document.querySelector('#editValueField input').required = false;
        document.querySelector('#editPercentageField input').required = true;
    }
}

function editAchievement(achievement) {
    document.getElementById('editId').value = achievement.id;
    document.getElementById('editType').value = achievement.type;
    document.getElementById('editTitle').value = achievement.title;
    document.getElementById('editValue').value = achievement.value || '';
    document.getElementById('editPercentage').value = achievement.percentage || '';
    document.getElementById('editDescription').value = achievement.description;
    document.getElementById('editIcon').value = achievement.icon;
    document.getElementById('editColor').value = achievement.color;
    document.getElementById('editSortOrder').value = achievement.sort_order;

    toggleEditFields();

    const modal = new bootstrap.Modal(document.getElementById('editAchievementModal'));
    modal.show();
}
</script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>