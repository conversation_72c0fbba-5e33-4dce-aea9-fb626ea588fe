<?php
$page_title = 'تعديل البكج - جديد';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = (int)$_GET['id'];

// جلب بيانات البكج
$stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$stmt->execute([$package_id]);
$package = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$package) {
    header('Location: packages.php');
    exit;
}

$success = '';
$error = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $price = $_POST['price'] ?? '';
    
    // التحقق البسيط
    if (empty($title) || empty($description) || empty($price)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!is_numeric($price) || $price <= 0) {
        $error = 'يرجى إدخال سعر صحيح';
    } else {
        // معالجة رفع الصورة
        $image_filename = $package['image']; // الاحتفاظ بالصورة الحالية
        
        if (isset($_FILES['image']) && $_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $upload_dir = '../uploads/products/';
            
            // التأكد من وجود المجلد
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $upload_result = upload_file($_FILES['image'], $upload_dir);
            
            if ($upload_result['success']) {
                // حذف الصورة القديمة
                if (!empty($package['image'])) {
                    $old_image_path = $upload_dir . $package['image'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                }
                $image_filename = $upload_result['filename'];
            } else {
                $error = 'خطأ في رفع الصورة: ' . $upload_result['message'];
            }
        }
        
        // تحديث البكج إذا لم يكن هناك خطأ
        if (empty($error)) {
            try {
                $stmt = $pdo->prepare("UPDATE packages SET title = ?, description = ?, price = ?, image = ? WHERE id = ?");
                $result = $stmt->execute([$title, $description, $price, $image_filename, $package_id]);
                
                if ($result) {
                    $affected_rows = $stmt->rowCount();
                    if ($affected_rows > 0) {
                        $success = 'تم تحديث البكج بنجاح!';
                        // إعادة جلب البيانات المحدثة
                        $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
                        $stmt->execute([$package_id]);
                        $package = $stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $success = 'لم يتم إجراء أي تغييرات على البيانات';
                    }
                } else {
                    $error = 'حدث خطأ أثناء تحديث البكج';
                }
            } catch (Exception $e) {
                $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل البكج (جديد)</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="packages.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى البكجات
        </a>
    </div>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">تعديل البكج: <?php echo htmlspecialchars($package['title']); ?></h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="title" class="form-label">عنوان البكج <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($package['title']); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $package['price']; ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف البكج <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($package['description']); ?></textarea>
            </div>
            
            <div class="mb-3">
                <label for="image" class="form-label">صورة البكج</label>
                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                <?php if (!empty($package['image'])): ?>
                <div class="mt-2">
                    <img src="../uploads/products/<?php echo $package['image']; ?>" alt="<?php echo htmlspecialchars($package['title']); ?>" class="img-thumbnail" style="max-height: 200px;">
                </div>
                <?php endif; ?>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> هذه النسخة الجديدة تدعم تعديل العنوان والوصف والسعر والصورة الرئيسية.
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="packages.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
