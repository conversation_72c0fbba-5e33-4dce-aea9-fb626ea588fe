<?php
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// معالجة حذف الوسائط
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $media_id = $_GET['delete'];
    
    // الحصول على معلومات الوسائط للحصول على اسم الملف
    $stmt = $pdo->prepare("SELECT file_url, type FROM media WHERE id = ?");
    $stmt->execute([$media_id]);
    $media = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // حذف الوسائط من قاعدة البيانات
    $delete_stmt = $pdo->prepare("DELETE FROM media WHERE id = ?");
    $delete_result = $delete_stmt->execute([$media_id]);
    
    if ($delete_result && $media) {
        // حذف ملف الوسائط من المجلد
        $file_path = "../uploads/media/" . $media['file_url'];
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        
        $success_message = "تم حذف الوسائط بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف الوسائط";
    }
}

// معالجة إضافة وسائط جديدة
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات المدخلة
    $caption = clean_input($_POST['caption']);
    
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $error = 'يرجى اختيار ملف للرفع';
    } else {
        // تحديد نوع الملف
        $file_type = '';
        $mime_type = mime_content_type($_FILES['file']['tmp_name']);
        
        if (strpos($mime_type, 'image') !== false) {
            $file_type = 'image';
        } elseif (strpos($mime_type, 'video') !== false) {
            $file_type = 'video';
        } else {
            $error = 'نوع الملف غير مدعوم. يرجى رفع صورة أو فيديو فقط.';
        }
        
        if (empty($error)) {
            // رفع الملف
            $upload_dir = '../uploads/media/';
            
            // التأكد من وجود المجلد
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $upload_result = upload_file($_FILES['file'], $upload_dir);
            
            if ($upload_result['success']) {
                // إضافة الوسائط إلى قاعدة البيانات
                $stmt = $pdo->prepare("INSERT INTO media (type, file_url, caption) VALUES (?, ?, ?)");
                $result = $stmt->execute([$file_type, $upload_result['filename'], $caption]);
                
                if ($result) {
                    $success = 'تمت إضافة الوسائط بنجاح';
                    // إعادة تعيين القيم
                    $caption = '';
                } else {
                    $error = 'حدث خطأ أثناء إضافة الوسائط';
                }
            } else {
                $error = $upload_result['message'];
            }
        }
    }
}

// استعلام للحصول على جميع الوسائط
$media_stmt = $pdo->query("SELECT * FROM media ORDER BY id DESC");
$media_items = $media_stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوسائط - ضوء الشمس</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3 sticky-top">
                    <a href="dashboard.php" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <span class="fs-4">ضوء الشمس</span>
                    </a>
                    <hr>
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="dashboard.php" class="nav-link text-white">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                        </li>
                        <li>
                            <a href="products.php" class="nav-link text-white">
                                <i class="fas fa-solar-panel"></i> المنتجات
                            </a>
                        </li>
                        <li>
                            <a href="packages.php" class="nav-link text-white">
                                <i class="fas fa-box"></i> البكجات
                            </a>
                        </li>
                        <li>
                            <a href="media.php" class="nav-link active">
                                <i class="fas fa-images"></i> معرض الوسائط
                            </a>
                        </li>
                        <li>
                            <a href="company.php" class="nav-link text-white">
                                <i class="fas fa-building"></i> معلومات الشركة
                            </a>
                        </li>
                    </ul>
                    <hr>
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2 fa-2x"></i>
                            <strong><?php echo $_SESSION['admin_username']; ?></strong>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                            <li><a class="dropdown-item" href="../index.php" target="_blank">عرض الموقع</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة معرض الوسائط</h1>
                </div>
                
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Add media form -->
                <div class="card admin-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">إضافة وسائط جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="file" class="form-label">اختر ملف (صورة أو فيديو) <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="file" name="file" accept="image/*,video/*" required>
                                    <small class="form-text text-muted">الحد الأقصى لحجم الملف: 10 ميجابايت</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="caption" class="form-label">وصف الوسائط</label>
                                    <input type="text" class="form-control" id="caption" name="caption" value="<?php echo isset($caption) ? $caption : ''; ?>">
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary">إضافة</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Media gallery -->
                <div class="card admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">معرض الوسائط</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($media_items)): ?>
                        <div class="alert alert-info">
                            لا توجد وسائط متاحة حالياً.
                        </div>
                        <?php else: ?>
                        <div class="row">
                            <?php foreach ($media_items as $media): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <div class="card h-100">
                                    <?php if ($media['type'] == 'image'): ?>
                                    <img src="../uploads/media/<?php echo $media['file_url']; ?>" class="card-img-top" alt="<?php echo $media['caption']; ?>" style="height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                    <video class="card-img-top" style="height: 200px; object-fit: cover;" controls>
                                        <source src="../uploads/media/<?php echo $media['file_url']; ?>" type="video/mp4">
                                        متصفحك لا يدعم تشغيل الفيديو.
                                    </video>
                                    <?php endif; ?>
                                    <div class="card-body">
                                        <p class="card-text"><?php echo $media['caption']; ?></p>
                                        <p class="text-muted small">
                                            <i class="fas <?php echo ($media['type'] == 'image') ? 'fa-image' : 'fa-video'; ?> me-1"></i> 
                                            <?php echo ($media['type'] == 'image') ? 'صورة' : 'فيديو'; ?>
                                        </p>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted"><?php echo date('Y-m-d', strtotime($media['created_at'])); ?></small>
                                            <a href="media.php?delete=<?php echo $media['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الوسائط؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
