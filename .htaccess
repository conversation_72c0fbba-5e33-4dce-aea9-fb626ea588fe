# ملف .htaccess لموقع شركة بريق ضوء الشمس
# إعدادات الأمان والأداء

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول إلى ملفات النظام الحساسة
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول إلى ملفات PHP الحساسة
<FilesMatch "\.(inc|conf|config)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات قاعدة البيانات
<FilesMatch "\.(sql|db|sqlite)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# حماية مجلد admin من البوتات
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/admin
    RewriteCond %{HTTP_USER_AGENT} (bot|crawl|spider) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تفعيل التخزين المؤقت للمتصفح
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات رفع الملفات
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# إعدادات الذاكرة
php_value memory_limit 256M

# تفعيل عرض الأخطاء (للتطوير فقط - احذف في الإنتاج)
# php_flag display_errors On
# php_flag display_startup_errors On

# إخفاء الأخطاء (للإنتاج)
php_flag display_errors Off
php_flag display_startup_errors Off
php_flag log_errors On

# إعادة توجيه الصفحات المحذوفة إلى الصفحة الرئيسية
ErrorDocument 404 /index.php

# حماية من هجمات SQL injection
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# حماية ملفات الإعدادات
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "config.example.php">
    Order allow,deny
    Deny from all
</Files>

# السماح بالوصول للملفات المطلوبة فقط
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# إعدادات HTTPS (إذا كان متوفر)
<IfModule mod_rewrite.c>
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>
