<?php
$page_title = 'إدارة المنتجات';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// معالجة حذف المنتج
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $product_id = $_GET['delete'];

    // الحصول على معلومات المنتج للحصول على اسم الصورة
    $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    // حذف المنتج من قاعدة البيانات
    $delete_stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
    $delete_result = $delete_stmt->execute([$product_id]);

    if ($delete_result && $product) {
        // حذف صورة المنتج من المجلد
        $image_path = "../uploads/products/" . $product['image'];
        if (file_exists($image_path)) {
            unlink($image_path);
        }

        $success_message = "تم حذف المنتج بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف المنتج";
    }
}

// استعلام للحصول على جميع المنتجات
$products_stmt = $pdo->query("SELECT * FROM products ORDER BY id DESC");
$products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة المنتجات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_product.php" class="btn btn-sm btn-primary">
            <i class="fas fa-plus"></i> إضافة منتج جديد
        </a>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<!-- Products table -->
<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المنتجات</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>القسم</th>
                        <th>السعر</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($products)): ?>
                    <tr>
                        <td colspan="7" class="text-center">لا توجد منتجات</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td><?php echo $product['id']; ?></td>
                            <td>
                                <img src="../uploads/products_images/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" width="50" height="50" class="rounded">
                            </td>
                            <td><?php echo $product['name']; ?></td>
                            <td>
                                <?php
                                    switch($product['category']) {
                                        case 'panel':
                                            echo 'ألواح شمسية';
                                            break;
                                        case 'inverter':
                                            echo 'إنفرترات';
                                            break;
                                        case 'battery':
                                            echo 'بطاريات';
                                            break;
                                        case 'accessory':
                                            echo 'إكسسوارات';
                                            break;
                                        default:
                                            echo $product['category'];
                                            break;
                                    }
                                ?>
                            </td>
                            <td><?php echo number_format($product['price']); ?> د.ع</td>
                            <td><?php echo date('Y-m-d', strtotime($product['created_at'])); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="edit_product.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="products.php?delete=<?php echo $product['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
