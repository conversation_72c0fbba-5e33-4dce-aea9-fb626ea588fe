<?php
require_once 'includes/header.php';

// استعلام للحصول على الإنفرترات
$inverters_stmt = $pdo->query("SELECT * FROM products WHERE category = 'inverter' ORDER BY id DESC");
$inverters = $inverters_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-5">
    <div class="container">
        <h1 class="text-center">الإنفرترات</h1>
        <p class="text-center lead">مجموعة متنوعة من الإنفرترات عالية الجودة</p>
    </div>
</div>

<!-- قسم الإنفرترات -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 mb-4">
                <!-- معلومات جانبية -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">معلومات عن الإنفرترات</h5>
                    </div>
                    <div class="card-body">
                        <p>الإنفرتر هو جهاز يقوم بتحويل التيار المستمر (DC) المنتج من الألواح الشمسية إلى تيار متردد (AC) يمكن استخدامه في المنزل أو المكتب.</p>
                        <p>نوفر مجموعة متنوعة من الإنفرترات بقدرات مختلفة تناسب احتياجاتك.</p>
                        <hr>
                        <h6>مميزات إنفرتراتنا:</h6>
                        <ul>
                            <li>كفاءة عالية في تحويل الطاقة</li>
                            <li>حماية متكاملة ضد ارتفاع الجهد والحرارة</li>
                            <li>تصميم مدمج وسهل التركيب</li>
                            <li>ضمان طويل الأمد</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-lg-9">
                <?php if (empty($inverters)): ?>
                <div class="alert alert-info">
                    لا توجد إنفرترات متاحة حالياً. يرجى التحقق لاحقاً.
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($inverters as $inverter): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card product-card h-100">
                            <?php
                            // الحصول على الصور الإضافية للمنتج
                            $additional_images_stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ?");
                            $additional_images_stmt->execute([$inverter['id']]);
                            $additional_images = $additional_images_stmt->fetchAll(PDO::FETCH_ASSOC);

                            // إذا كانت هناك صور إضافية، نعرض شرائح الصور
                            if (!empty($additional_images)):
                            ?>
                            <div id="carousel-<?php echo $inverter['id']; ?>" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner">
                                    <div class="carousel-item active">
                                        <img src="uploads/products_images/<?php echo $inverter['image']; ?>" class="d-block w-100" alt="<?php echo $inverter['name']; ?>" style="height: 200px; object-fit: cover;">
                                    </div>
                                    <?php foreach ($additional_images as $image): ?>
                                    <div class="carousel-item">
                                        <img src="uploads/products_images/<?php echo $image['image_url']; ?>" class="d-block w-100" alt="<?php echo $inverter['name']; ?>" style="height: 200px; object-fit: cover;">
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#carousel-<?php echo $inverter['id']; ?>" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">السابق</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#carousel-<?php echo $inverter['id']; ?>" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">التالي</span>
                                </button>
                            </div>
                            <?php else: ?>
                            <img src="uploads/products_images/<?php echo $inverter['image']; ?>" class="card-img-top" alt="<?php echo $inverter['name']; ?>" style="height: 200px; object-fit: cover;">
                            <?php endif; ?>

                            <div class="card-body">
                                <h5 class="card-title"><?php echo $inverter['name']; ?></h5>
                                <p class="card-text"><?php echo substr($inverter['description'], 0, 80); ?>...</p>
                                <p class="price"><?php echo number_format($inverter['price']); ?> د.ع</p>
                                <div class="d-grid gap-2">
                                    <a href="product-details.php?id=<?php echo $inverter['id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-info-circle"></i> عرض التفاصيل
                                    </a>
                                    <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن ' . $inverter['name']); ?>" class="btn btn-success" target="_blank">
                                        <i class="fab fa-whatsapp"></i> استفسار عبر واتساب
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
