<?php
$page_title = 'إضافة بكج جديد';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

$error = '';
$success = '';

// الحصول على قائمة المنتجات
$panels_stmt = $pdo->query("SELECT * FROM products WHERE category = 'panel' ORDER BY name");
$panels = $panels_stmt->fetchAll(PDO::FETCH_ASSOC);

$inverters_stmt = $pdo->query("SELECT * FROM products WHERE category = 'inverter' ORDER BY name");
$inverters = $inverters_stmt->fetchAll(PDO::FETCH_ASSOC);

$batteries_stmt = $pdo->query("SELECT * FROM products WHERE category = 'battery' ORDER BY name");
$batteries = $batteries_stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة نموذج إضافة البكج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات المدخلة
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $price = clean_input($_POST['price']);

    // بيانات الألواح الشمسية
    $panel_name = clean_input($_POST['panel_name']);
    $panel_count = clean_input($_POST['panel_count']);
    $panel_power = clean_input($_POST['panel_power']);

    // بيانات البطاريات
    $battery_name = clean_input($_POST['battery_name']);
    $battery_count = clean_input($_POST['battery_count']);
    $battery_power = clean_input($_POST['battery_power']);

    // بيانات الإنفرترات
    $inverter_name = clean_input($_POST['inverter_name']);
    $inverter_count = clean_input($_POST['inverter_count']);
    $inverter_power = clean_input($_POST['inverter_power']);

    if (empty($title) || empty($description) || empty($price)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!is_numeric($price) || $price <= 0) {
        $error = 'يرجى إدخال سعر صحيح';
    } elseif (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        $error = 'يرجى اختيار صورة للبكج';
    } else {
        // رفع الصورة الرئيسية للبكج
        $upload_dir = '../uploads/packages_images/';

        // التأكد من وجود المجلد
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $upload_result = upload_file($_FILES['image'], $upload_dir);

        if ($upload_result['success']) {
            // رفع صورة اللوح الشمسي
            $panel_image = '';
            if (isset($_FILES['panel_image']) && $_FILES['panel_image']['error'] === UPLOAD_ERR_OK) {
                $panel_upload_result = upload_file($_FILES['panel_image'], $upload_dir);
                if ($panel_upload_result['success']) {
                    $panel_image = $panel_upload_result['filename'];
                }
            }

            // رفع صورة البطارية
            $battery_image = '';
            if (isset($_FILES['battery_image']) && $_FILES['battery_image']['error'] === UPLOAD_ERR_OK) {
                $battery_upload_result = upload_file($_FILES['battery_image'], $upload_dir);
                if ($battery_upload_result['success']) {
                    $battery_image = $battery_upload_result['filename'];
                }
            }

            // رفع صورة الإنفرتر
            $inverter_image = '';
            if (isset($_FILES['inverter_image']) && $_FILES['inverter_image']['error'] === UPLOAD_ERR_OK) {
                $inverter_upload_result = upload_file($_FILES['inverter_image'], $upload_dir);
                if ($inverter_upload_result['success']) {
                    $inverter_image = $inverter_upload_result['filename'];
                }
            }

            // إنشاء مصفوفة لتفاصيل المكونات
            $components = [
                'panel' => [
                    'name' => $panel_name,
                    'count' => $panel_count,
                    'power' => $panel_power,
                    'image' => $panel_image
                ],
                'battery' => [
                    'name' => $battery_name,
                    'count' => $battery_count,
                    'power' => $battery_power,
                    'image' => $battery_image
                ],
                'inverter' => [
                    'name' => $inverter_name,
                    'count' => $inverter_count,
                    'power' => $inverter_power,
                    'image' => $inverter_image
                ]
            ];

            // تحويل مصفوفة المكونات إلى JSON
            $components_json = json_encode($components);

            // إنشاء مصفوفة فارغة للمنتجات
            $empty_product_ids = json_encode([]);

            // إضافة البكج إلى قاعدة البيانات
            $stmt = $pdo->prepare("INSERT INTO packages (title, description, image, price, components, product_ids) VALUES (?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([$title, $description, $upload_result['filename'], $price, $components_json, $empty_product_ids]);

            if ($result) {
                $success = 'تمت إضافة البكج بنجاح';
                // إعادة تعيين القيم
                $title = $description = '';
                $price = 0;
                $panel_name = $panel_count = $panel_power = '';
                $battery_name = $battery_count = $battery_power = '';
                $inverter_name = $inverter_count = $inverter_power = '';
            } else {
                $error = 'حدث خطأ أثناء إضافة البكج';
            }
        } else {
            $error = $upload_result['message'];
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إضافة بكج جديد</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="packages.php" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة إلى قائمة البكجات
                        </a>
                    </div>
                </div>

                <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Add package form -->
                <div class="card admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">إضافة بكج جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">عنوان البكج <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?php echo isset($title) ? $title : ''; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo isset($price) ? $price : ''; ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف البكج <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($description) ? $description : ''; ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="image" class="form-label">صورة البكج <span class="text-danger">*</span></label>
                                <input type="file" class="form-control image-upload" id="image" name="image" accept="image/*" data-preview="image-preview" required>
                                <div class="mt-2">
                                    <img id="image-preview" src="#" alt="معاينة الصورة" class="img-thumbnail" style="max-height: 200px; display: none;">
                                </div>
                            </div>

                            <h5 class="mt-4 mb-3">تفاصيل مكونات البكج</h5>

                            <!-- تفاصيل الألواح الشمسية -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-solar-panel me-2"></i> تفاصيل الألواح الشمسية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_name" class="form-label">اسم اللوح</label>
                                            <input type="text" class="form-control" id="panel_name" name="panel_name" value="<?php echo isset($panel_name) ? $panel_name : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_count" class="form-label">عدد الألواح</label>
                                            <input type="number" class="form-control" id="panel_count" name="panel_count" min="0" value="<?php echo isset($panel_count) ? $panel_count : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_power" class="form-label">قدرة اللوح (واط)</label>
                                            <input type="text" class="form-control" id="panel_power" name="panel_power" value="<?php echo isset($panel_power) ? $panel_power : ''; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="panel_image" class="form-label">صورة اللوح</label>
                                        <input type="file" class="form-control image-upload" id="panel_image" name="panel_image" accept="image/*" data-preview="panel-image-preview">
                                        <div class="mt-2">
                                            <img id="panel-image-preview" src="#" alt="معاينة صورة اللوح" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل البطاريات -->
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-car-battery me-2"></i> تفاصيل بطاريات الليثيوم</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_name" class="form-label">اسم البطارية</label>
                                            <input type="text" class="form-control" id="battery_name" name="battery_name" value="<?php echo isset($battery_name) ? $battery_name : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_count" class="form-label">عدد البطاريات</label>
                                            <input type="number" class="form-control" id="battery_count" name="battery_count" min="0" value="<?php echo isset($battery_count) ? $battery_count : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_power" class="form-label">قدرة البطارية (أمبير)</label>
                                            <input type="text" class="form-control" id="battery_power" name="battery_power" value="<?php echo isset($battery_power) ? $battery_power : ''; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="battery_image" class="form-label">صورة البطارية</label>
                                        <input type="file" class="form-control image-upload" id="battery_image" name="battery_image" accept="image/*" data-preview="battery-image-preview">
                                        <div class="mt-2">
                                            <img id="battery-image-preview" src="#" alt="معاينة صورة البطارية" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل الإنفرترات -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-plug me-2"></i> تفاصيل الإنفرترات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_name" class="form-label">اسم الإنفرتر</label>
                                            <input type="text" class="form-control" id="inverter_name" name="inverter_name" value="<?php echo isset($inverter_name) ? $inverter_name : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_count" class="form-label">عدد الإنفرترات</label>
                                            <input type="number" class="form-control" id="inverter_count" name="inverter_count" min="0" value="<?php echo isset($inverter_count) ? $inverter_count : ''; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_power" class="form-label">قدرة الإنفرتر (واط)</label>
                                            <input type="text" class="form-control" id="inverter_power" name="inverter_power" value="<?php echo isset($inverter_power) ? $inverter_power : ''; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="inverter_image" class="form-label">صورة الإنفرتر</label>
                                        <input type="file" class="form-control image-upload" id="inverter_image" name="inverter_image" accept="image/*" data-preview="inverter-image-preview">
                                        <div class="mt-2">
                                            <img id="inverter-image-preview" src="#" alt="معاينة صورة الإنفرتر" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظة: تم استبدال قسم اختيار المنتجات بقسم تفاصيل مكونات البكج -->
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle me-2"></i> يمكنك إضافة تفاصيل مكونات البكج في الأقسام أعلاه. هذه التفاصيل ستظهر في صفحة تفاصيل البكج.
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="reset" class="btn btn-secondary">إعادة تعيين</button>
                                <button type="submit" class="btn btn-primary">إضافة البكج</button>
                            </div>
                        </form>
                    </div>
                </div>

    <script>
        // معاينة الصور عند اختيارها
        document.addEventListener('DOMContentLoaded', function() {
            // وظيفة لمعاينة الصور
            function setupImagePreview(inputSelector) {
                const input = document.querySelector(inputSelector);
                if (input) {
                    input.addEventListener('change', function() {
                        const previewId = this.getAttribute('data-preview');
                        const preview = document.getElementById(previewId);

                        if (this.files && this.files[0]) {
                            const reader = new FileReader();

                            reader.onload = function(e) {
                                preview.src = e.target.result;
                                preview.style.display = 'block';
                            };

                            reader.readAsDataURL(this.files[0]);
                        }
                    });
                }
            }

            // إعداد معاينة لجميع حقول الصور
            setupImagePreview('#image');
            setupImagePreview('#panel_image');
            setupImagePreview('#battery_image');
            setupImagePreview('#inverter_image');
        });
    </script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
