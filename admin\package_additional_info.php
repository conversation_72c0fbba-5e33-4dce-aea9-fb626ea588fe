<?php
$page_title = 'إدارة معلومات البكجات الإضافية';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

$success = '';
$error = '';

// معالجة الحذف
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $delete_id = $_GET['delete'];
    try {
        $stmt = $pdo->prepare("DELETE FROM package_additional_info WHERE id = ?");
        $stmt->execute([$delete_id]);
        $success = 'تم حذف العنصر بنجاح!';
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
    }
}

// معالجة تغيير الحالة
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $toggle_id = $_GET['toggle'];
    try {
        $stmt = $pdo->prepare("UPDATE package_additional_info SET is_active = NOT is_active WHERE id = ?");
        $stmt->execute([$toggle_id]);
        $success = 'تم تغيير حالة العنصر بنجاح!';
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء تغيير الحالة: ' . $e->getMessage();
    }
}

// جلب جميع العناصر
$stmt = $pdo->query("SELECT * FROM package_additional_info ORDER BY section_type, display_order");
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// تجميع العناصر حسب النوع
$technical_items = array_filter($items, function($item) {
    return $item['section_type'] === 'technical';
});

$services_items = array_filter($items, function($item) {
    return $item['section_type'] === 'services';
});

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إدارة معلومات البكجات الإضافية</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_package_info.php" class="btn btn-sm btn-primary">
            <i class="fas fa-plus"></i> إضافة معلومة جديدة
        </a>
    </div>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<!-- المواصفات الفنية -->
<div class="card admin-card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> المواصفات الفنية</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($technical_items)): ?>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الترتيب</th>
                        <th>الأيقونة</th>
                        <th>العنوان</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($technical_items as $item): ?>
                    <tr>
                        <td><?php echo $item['display_order']; ?></td>
                        <td><i class="<?php echo $item['icon']; ?> text-primary"></i></td>
                        <td><?php echo htmlspecialchars($item['title']); ?></td>
                        <td><?php echo nl2br(htmlspecialchars(substr($item['description'], 0, 100))); ?>...</td>
                        <td>
                            <span class="badge <?php echo $item['is_active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                <?php echo $item['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="edit_package_info.php?id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="?toggle=<?php echo $item['id']; ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-toggle-<?php echo $item['is_active'] ? 'on' : 'off'; ?>"></i>
                                </a>
                                <a href="?delete=<?php echo $item['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا العنصر؟')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-4">
            <i class="fas fa-info-circle text-muted fs-1 mb-3"></i>
            <p class="text-muted">لا توجد مواصفات فنية مضافة بعد.</p>
            <a href="add_package_info.php?type=technical" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i> إضافة مواصفة فنية
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- خدمات ما بعد البيع -->
<div class="card admin-card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="fas fa-handshake me-2"></i> خدمات ما بعد البيع</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($services_items)): ?>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الترتيب</th>
                        <th>الأيقونة</th>
                        <th>العنوان</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($services_items as $item): ?>
                    <tr>
                        <td><?php echo $item['display_order']; ?></td>
                        <td><i class="<?php echo $item['icon']; ?> text-success"></i></td>
                        <td><?php echo htmlspecialchars($item['title']); ?></td>
                        <td><?php echo nl2br(htmlspecialchars(substr($item['description'], 0, 100))); ?>...</td>
                        <td>
                            <span class="badge <?php echo $item['is_active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                <?php echo $item['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="edit_package_info.php?id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="?toggle=<?php echo $item['id']; ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-toggle-<?php echo $item['is_active'] ? 'on' : 'off'; ?>"></i>
                                </a>
                                <a href="?delete=<?php echo $item['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا العنصر؟')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-4">
            <i class="fas fa-info-circle text-muted fs-1 mb-3"></i>
            <p class="text-muted">لا توجد خدمات مضافة بعد.</p>
            <a href="add_package_info.php?type=services" class="btn btn-success">
                <i class="fas fa-plus me-2"></i> إضافة خدمة
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
