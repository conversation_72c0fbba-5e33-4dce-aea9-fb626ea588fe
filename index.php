<?php
require_once 'includes/header.php';

// استعلام للحصول على البكجات المميزة
$featured_packages_stmt = $pdo->query("SELECT * FROM packages ORDER BY id DESC LIMIT 3");
$featured_packages = $featured_packages_stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة بيانات المكونات لكل بكج مميز
foreach ($featured_packages as &$package) {
    $package['components'] = isset($package['components']) ? json_decode($package['components'], true) : null;

    // حساب القدرة الإجمالية
    $package['total_power'] = 0;
    if (!empty($package['components']['panel']['count']) && !empty($package['components']['panel']['power'])) {
        $panel_power = (int)$package['components']['panel']['power'];
        $panel_count = (int)$package['components']['panel']['count'];
        $package['total_power'] = $panel_power * $panel_count;
    }
}

// استعلام للحصول على المنتجات المميزة
$featured_products_stmt = $pdo->query("SELECT * FROM products ORDER BY id DESC LIMIT 8");
$featured_products = $featured_products_stmt->fetchAll(PDO::FETCH_ASSOC);

// استعلام للحصول على صور العرض النشطة
$sliders = [];
try {
    // التحقق من وجود جدول صور العرض
    $stmt = $pdo->query("SHOW TABLES LIKE 'sliders'");
    $table_exists = ($stmt->rowCount() > 0);

    if ($table_exists) {
        $stmt = $pdo->query("SELECT * FROM sliders WHERE status = 1 ORDER BY order_number ASC, id ASC");
        $sliders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // تجاهل الخطأ
}
?>

<!-- السلايدر الرئيسي -->
<div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-indicators">
        <?php for ($i = 0; $i < count($sliders); $i++): ?>
        <button type="button" data-bs-target="#mainCarousel" data-bs-slide-to="<?php echo $i; ?>" <?php echo ($i === 0) ? 'class="active"' : ''; ?> aria-current="<?php echo ($i === 0) ? 'true' : 'false'; ?>" aria-label="Slide <?php echo $i + 1; ?>"></button>
        <?php endfor; ?>

        <?php if (empty($sliders)): ?>
        <button type="button" data-bs-target="#mainCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
        <?php endif; ?>
    </div>

    <div class="carousel-inner">
        <?php if (!empty($sliders)): ?>
            <?php foreach ($sliders as $index => $slider): ?>
            <div class="carousel-item <?php echo ($index === 0) ? 'active' : ''; ?>">
                <section class="hero-banner modern-banner" style="background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('<?php echo $slider['image'] == 'default-slider.jpg' ? 'assets/img/solar-banner.jpg' : 'uploads/sliders/' . $slider['image']; ?>') !important; background-size: cover !important; background-position: center !important;">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-12 text-center">
                                <!-- تم إزالة النص والأزرار من السلايدر الرئيسي -->
                            </div>
                        </div>
                        <div class="banner-overlay"></div>
                    </div>
                </section>
            </div>
            <?php endforeach; ?>
        <?php else: ?>
            <!-- صورة عرض افتراضية في حالة عدم وجود صور في قاعدة البيانات -->
            <div class="carousel-item active">
                <section class="hero-banner modern-banner" style="background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('assets/img/solar-banner.jpg') !important; background-size: cover !important; background-position: center !important;">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-12 text-center">
                                <!-- تم إزالة النص والأزرار من السلايدر الرئيسي -->
                            </div>
                        </div>
                        <div class="banner-overlay"></div>
                    </div>
                </section>
            </div>
        <?php endif; ?>
    </div>

    <?php if (count($sliders) > 1 || empty($sliders)): ?>
    <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">السابق</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">التالي</span>
    </button>
    <?php endif; ?>
</div>

<!-- مقدمة عن الشركة - محسنة -->
<section class="py-5 about-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-5 mb-lg-0 animate-on-scroll">
                <div class="about-image-wrapper">
                    <img src="assets/img/about-solar.jpg" alt="شركة بريق ضوء الشمس" class="img-fluid rounded-lg about-image">
                    <div class="">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 ps-lg-5">
                <div class="section-title mb-4">
                    <span class="subtitle">تعرف علينا</span>
                    <h2 class="title">من نحن</h2>
                </div>
                <p class="lead mb-4">شركة متخصصة في توفير حلول الطاقة الشمسية المتكاملة للمنازل والشركات.</p>
                <p class="mb-4">نقدم أفضل المنتجات من الألواح الشمسية والإنفرترات والبطاريات بأسعار منافسة وجودة عالية. فريقنا من الخبراء جاهز لتقديم الاستشارة والتركيب والصيانة.</p>

                <div class="row mb-4">
                    <div class="col-6">
                        <div class="about-feature">
                            <i class="fas fa-certificate feature-icon-sm"></i>
                            <h5>منتجات معتمدة</h5>
                            <p>جميع منتجاتنا معتمدة عالمياً</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="about-feature">
                            <i class="fas fa-tools feature-icon-sm"></i>
                            <h5>خدمة متكاملة</h5>
                            <p>من التصميم إلى التركيب والصيانة</p>
                        </div>
                    </div>
                </div>

                <a href="contact.php" class="btn btn-primary btn-lg rounded-pill">
                    <i class="fas fa-phone-alt me-2"></i>تواصل معنا
                </a>
            </div>
        </div>
    </div>
</section>

<!-- مميزات الطاقة الشمسية - محسنة -->
<section class="py-5 features-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-7 text-center">
                <div class="section-title">
                    <span class="subtitle">المميزات</span>
                    <h2 class="title">لماذا الطاقة الشمسية؟</h2>
                    <p class="section-description">اكتشف الفوائد العديدة لاستخدام الطاقة الشمسية في منزلك أو مشروعك</p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4 animate-on-scroll">
                <div class="feature-card">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="feature-content">
                        <h4>توفير في فواتير الكهرباء</h4>
                        <p>خفض تكاليف الكهرباء بنسبة تصل إلى 90% واسترداد تكلفة الاستثمار خلال فترة قصيرة.</p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-value">90%</span>
                                <span class="stat-label">توفير</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 animate-on-scroll" data-delay="200">
                <div class="feature-card active">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="feature-content">
                        <h4>صديقة للبيئة</h4>
                        <p>طاقة نظيفة ومتجددة تساهم في تقليل انبعاثات الكربون والحفاظ على البيئة.</p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-value">100%</span>
                                <span class="stat-label">نظيفة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 animate-on-scroll" data-delay="400">
                <div class="feature-card">
                    <div class="feature-icon-wrapper">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="feature-content">
                        <h4>استقلالية في الطاقة</h4>
                        <p>لا مزيد من انقطاع التيار الكهربائي. استمتع بمصدر طاقة موثوق على مدار الساعة.</p>
                        <div class="feature-stats">
                            <div class="stat">
                                <span class="stat-value">24/7</span>
                                <span class="stat-label">توفر</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <a href="contact.php" class="btn btn-outline-primary btn-lg rounded-pill">
                <i class="fas fa-info-circle me-2"></i>استفسر عن المزيد من المميزات
            </a>
        </div>
    </div>
</section>

<!-- البكجات المميزة -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5 animate-on-scroll">
            <div class="section-title">
                <span class="subtitle">البكجات الجاهزة</span>
                <h2 class="title">حلول متكاملة للطاقة الشمسية</h2>
                <p class="section-description">اختر من بين مجموعة متنوعة من البكجات الجاهزة المصممة لتلبية احتياجاتك</p>
            </div>
        </div>

        <?php if (empty($featured_packages)): ?>
        <div class="text-center py-5">
            <i class="fas fa-box-open text-muted" style="font-size: 4rem;"></i>
            <h3 class="text-muted mt-3">لا توجد بكجات متاحة حالياً</h3>
            <p class="text-muted">يرجى التحقق لاحقاً أو التواصل معنا للاستفسار</p>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($featured_packages as $package): ?>
            <div class="col-lg-4 col-md-6 animate-on-scroll">
                <div class="card package-card border-0 shadow-lg h-100" style="transition: transform 0.3s ease, box-shadow 0.3s ease;">
                    <!-- صورة البكج الكاملة -->
                    <div class="position-relative home-package-image-container">
                        <img src="uploads/packages_images/<?php echo $package['image']; ?>" class="card-img-top home-package-image" alt="<?php echo $package['title']; ?>">

                        <!-- شارة السعر -->
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-success fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-tag me-1"></i> <?php echo number_format($package['price']); ?> د.ع
                            </span>
                        </div>

                        <!-- شارة القدرة الإجمالية -->
                        <?php if ($package['total_power'] > 0): ?>
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-warning text-dark fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-bolt me-1"></i> <?php echo number_format($package['total_power']); ?> واط
                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- شارة متوفر -->
                        <div class="position-absolute bottom-0 end-0 m-3">
                            <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-check-circle me-1"></i> متوفر
                            </span>
                        </div>
                    </div>

                    <!-- محتوى البطاقة -->
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <h4 class="card-title fw-bold text-primary mb-2"><?php echo $package['title']; ?></h4>
                            <p class="card-text text-muted mb-3"><?php echo substr($package['description'], 0, 100); ?>...</p>
                        </div>

                        <!-- ملخص سريع للمكونات -->
                        <?php if (!empty($package['components'])): ?>
                        <div class="components-quick-summary mb-4">
                            <h6 class="fw-bold text-secondary mb-2">
                                <i class="fas fa-cogs me-2"></i> يشمل:
                            </h6>

                            <div class="row g-1">
                                <!-- الألواح الشمسية -->
                                <?php if (!empty($package['components']['panel']['count'])): ?>
                                <div class="col-12">
                                    <small class="d-flex align-items-center text-muted">
                                        <i class="fas fa-solar-panel text-primary me-2"></i>
                                        <?php echo $package['components']['panel']['count']; ?> لوح شمسي
                                        <?php if (!empty($package['components']['panel']['power'])): ?>
                                            (<?php echo $package['components']['panel']['power']; ?> واط)
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>

                                <!-- البطاريات -->
                                <?php if (!empty($package['components']['battery']['count'])): ?>
                                <div class="col-12">
                                    <small class="d-flex align-items-center text-muted">
                                        <i class="fas fa-car-battery text-warning me-2"></i>
                                        <?php echo $package['components']['battery']['count']; ?> بطارية
                                        <?php if (!empty($package['components']['battery']['power'])): ?>
                                            (<?php echo $package['components']['battery']['power']; ?> أمبير)
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>

                                <!-- الإنفرترات -->
                                <?php if (!empty($package['components']['inverter']['count'])): ?>
                                <div class="col-12">
                                    <small class="d-flex align-items-center text-muted">
                                        <i class="fas fa-plug text-info me-2"></i>
                                        <?php echo $package['components']['inverter']['count']; ?> إنفرتر
                                        <?php if (!empty($package['components']['inverter']['power'])): ?>
                                            (<?php echo $package['components']['inverter']['power']; ?> واط)
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- الأزرار -->
                        <div class="d-grid gap-2">
                            <a href="package-details.php?id=<?php echo $package['id']; ?>" class="btn btn-primary rounded-pill">
                                <i class="fas fa-eye me-2"></i> عرض التفاصيل الكاملة
                            </a>
                            <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن بكج ' . $package['title'] . ' بسعر ' . number_format($package['price']) . ' د.ع'); ?>" class="btn btn-success rounded-pill" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i> استفسار عبر واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center mt-5">
            <a href="packages.php" class="btn btn-outline-primary btn-lg rounded-pill">
                <i class="fas fa-th-large me-2"></i> عرض جميع البكجات
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- المنتجات المميزة -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5 animate-on-scroll">
            <h2 class="mb-3">منتجاتنا المميزة</h2>
            <p class="lead text-muted mx-auto" style="max-width: 700px;">تصفح مجموعة واسعة من منتجات الطاقة الشمسية عالية الجودة</p>
        </div>

        <?php if (empty($featured_products)): ?>
        <div class="alert alert-info">
            لا توجد منتجات متاحة حالياً. يرجى التحقق لاحقاً.
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($featured_products as $product): ?>
            <div class="col-lg-3 col-md-4 col-sm-6 animate-on-scroll">
                <div class="card product-card h-100 border-0 shadow-lg" style="transition: transform 0.3s ease;">
                    <?php
                    // استخدام صورة المنتج الرئيسية
                    $main_image = !empty($product['image']) ? $product['image'] : 'default-product.jpg';
                    ?>
                    <!-- صورة المنتج المرنة -->
                    <div class="position-relative featured-product-image-container">
                        <img src="uploads/products_images/<?php echo $main_image; ?>" class="card-img-top featured-product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">

                        <!-- شارة الفئة -->
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-primary">
                                <?php
                                switch($product['category']) {
                                    case 'panel': echo 'ألواح شمسية'; break;
                                    case 'inverter': echo 'إنفرترات'; break;
                                    case 'battery': echo 'بطاريات'; break;
                                    case 'accessory': echo 'إكسسوارات'; break;
                                    default: echo $product['category']; break;
                                }
                                ?>
                            </span>
                        </div>

                        <!-- شارة متوفر -->
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-success">
                                <i class="fas fa-check-circle me-1"></i> متوفر
                            </span>
                        </div>

                        <!-- overlay للتفاصيل -->
                        <div class="product-overlay">
                            <a href="product-details.php?id=<?php echo $product['id']; ?>" class="btn btn-primary btn-sm rounded-pill">
                                <i class="fas fa-eye me-1"></i> عرض التفاصيل
                            </a>
                        </div>
                    </div>

                    <!-- محتوى البطاقة -->
                    <div class="card-body p-3 d-flex flex-column">
                        <h5 class="card-title product-title fw-bold text-primary mb-2"><?php echo htmlspecialchars($product['name']); ?></h5>
                        <p class="card-text product-description text-muted flex-grow-1 mb-3">
                            <?php echo substr($product['description'], 0, 80); ?>...
                        </p>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="h5 text-success mb-0 fw-bold"><?php echo number_format($product['price']); ?> د.ع</span>
                        </div>
                        <div class="mt-auto">
                            <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن منتج ' . $product['name'] . ' بسعر ' . number_format($product['price']) . ' د.ع'); ?>" class="btn btn-success w-100 rounded-pill" target="_blank">
                                <i class="fab fa-whatsapp me-1"></i> استفسار عبر واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center mt-4">
            <a href="products.php" class="btn btn-outline-primary btn-lg rounded-pill">
                <i class="fas fa-th-large me-1"></i> عرض جميع المنتجات
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- CSS إضافي للتحسينات -->
<style>
/* تحسين عرض البكجات في الصفحة الرئيسية */
.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.package-card img:hover {
    transform: scale(1.02);
}

/* تحسين عرض صور البكجات في الصفحة الرئيسية */
.home-package-image-container {
    background: #f8f9fa;
    border-radius: 0.375rem 0.375rem 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 220px;
    overflow: hidden;
}

.home-package-image {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    transition: transform 0.3s ease;
    padding: 15px;
    background: transparent;
}

/* ملخص المكونات السريع */
.components-quick-summary {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #dee2e6;
}

.components-quick-summary small {
    font-size: 0.85rem;
    line-height: 1.4;
}

/* تحسين الشارات */
.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.position-absolute .badge.bg-success {
    background-color: rgba(40, 167, 69, 0.9) !important;
}

.position-absolute .badge.bg-warning {
    background-color: rgba(255, 193, 7, 0.9) !important;
}

.position-absolute .badge.bg-primary {
    background-color: rgba(13, 110, 253, 0.9) !important;
}

/* تحسين الأزرار */
.btn {
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

/* تحسينات الموبايل */
@media (max-width: 768px) {
    .home-package-image-container {
        min-height: 200px;
    }

    .home-package-image {
        padding: 10px;
    }

    .components-quick-summary {
        padding: 10px;
    }

    .badge {
        font-size: 0.75rem !important;
        padding: 0.5rem 1rem !important;
    }

    .card-title {
        font-size: 1.1rem;
    }
}

/* تحسين إضافي للصور */
.home-package-image {
    /* إزالة أي قيود على الارتفاع */
    max-height: none !important;
    /* السماح للصورة بأن تأخذ حجمها الطبيعي */
    height: auto !important;
    /* ضمان عدم تشويه الصورة */
    object-fit: contain !important;
}

/* تحسين عرض المنتجات المميزة */
.featured-product-image-container {
    background: #f8f9fa;
    border-radius: 0.375rem 0.375rem 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 220px;
    overflow: hidden;
    position: relative;
}

.featured-product-image {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    transition: transform 0.3s ease;
    padding: 15px;
    background: transparent;
}

.featured-product-image:hover {
    transform: scale(1.02);
}

/* تحسين overlay المنتجات */
.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-product-image-container:hover .product-overlay {
    opacity: 1;
}

/* تحسين بطاقات المنتجات */
.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

/* تحسين الشارات في المنتجات المميزة */
.featured-product-image-container .badge {
    font-weight: 600;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.featured-product-image-container .badge.bg-primary {
    background-color: rgba(13, 110, 253, 0.9) !important;
}

.featured-product-image-container .badge.bg-success {
    background-color: rgba(40, 167, 69, 0.9) !important;
}

/* تحسين العناوين */
.section-title .subtitle {
    color: #007bff;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.section-title .title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
}

.section-description {
    color: #6c757d;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* تحسينات إضافية للمنتجات المميزة */
.product-title {
    transition: color 0.3s ease;
}

.product-card:hover .product-title {
    color: #007bff !important;
}

/* تحسينات الموبايل للمنتجات المميزة */
@media (max-width: 768px) {
    .featured-product-image-container {
        min-height: 200px;
    }

    .featured-product-image {
        padding: 10px;
    }

    .featured-product-image-container .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }
}

/* تحسين إضافي للصور المميزة */
.featured-product-image {
    /* إزالة أي قيود على الارتفاع */
    max-height: none !important;
    /* السماح للصورة بأن تأخذ حجمها الطبيعي */
    height: auto !important;
    /* ضمان عدم تشويه الصورة */
    object-fit: contain !important;
}
</style>

<?php require_once 'includes/footer.php'; ?>
