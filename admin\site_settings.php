<?php
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التأكد من وجود مجلد الصور
$upload_dir = "../uploads/logo";
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// التحقق من وجود جدول الإعدادات
$table_exists = false;
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'site_settings'");
    $table_exists = ($stmt->rowCount() > 0);
} catch (PDOException $e) {
    // تجاهل الخطأ
}

// إذا لم يكن الجدول موجودًا، عرض رسالة للمستخدم
if (!$table_exists) {
    $_SESSION['warning_message'] = "جدول إعدادات الموقع غير موجود في قاعدة البيانات. يرجى النقر على زر 'إنشاء جدول الإعدادات' أدناه.";
}

// إنشاء جدول الإعدادات إذا تم طلب ذلك
if (isset($_GET['create_table']) && $_GET['create_table'] == 'true') {
    include_once 'create_settings_table.php';
    header("Location: site_settings.php");
    exit;
}

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // استلام البيانات من النموذج
    $site_name = clean_input($_POST['site_name']);
    $site_slogan = clean_input($_POST['site_slogan']);
    $primary_color = clean_input($_POST['primary_color']);
    $secondary_color = clean_input($_POST['secondary_color']);
    $footer_text = clean_input($_POST['footer_text']);

    // التحقق من البيانات المطلوبة
    if (empty($site_name)) {
        $_SESSION['error_message'] = "يرجى إدخال اسم الموقع";
    } else {
        // معالجة الشعار
        $logo_name = null;
        if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/svg+xml'];
            $max_size = 5 * 1024 * 1024; // 5 ميجابايت

            if (!in_array($_FILES['site_logo']['type'], $allowed_types)) {
                $_SESSION['error_message'] = "نوع الملف غير مسموح به. يرجى رفع صورة بصيغة JPEG أو PNG أو GIF أو SVG";
            } elseif ($_FILES['site_logo']['size'] > $max_size) {
                $_SESSION['error_message'] = "حجم الصورة كبير جدًا. الحد الأقصى هو 5 ميجابايت";
            } else {
                // إنشاء اسم فريد للصورة
                $logo_name = uniqid() . '_' . $_FILES['site_logo']['name'];
                $upload_path = $upload_dir . '/' . $logo_name;

                // نقل الصورة إلى المجلد المحدد
                if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $upload_path)) {
                    // تم رفع الصورة بنجاح
                    // تحديث قيمة الشعار في قاعدة البيانات
                    $stmt = $pdo->prepare("UPDATE site_settings SET setting_value = ? WHERE setting_key = 'site_logo'");
                    $stmt->execute([$logo_name]);
                } else {
                    $_SESSION['error_message'] = "حدث خطأ أثناء رفع الصورة";
                }
            }
        }

        // إذا لم تكن هناك أخطاء، قم بتحديث الإعدادات
        if (!isset($_SESSION['error_message'])) {
            // تحديث الإعدادات في قاعدة البيانات
            $settings = [
                'site_name' => $site_name,
                'site_slogan' => $site_slogan,
                'primary_color' => $primary_color,
                'secondary_color' => $secondary_color,
                'footer_text' => $footer_text
            ];

            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare("UPDATE site_settings SET setting_value = ? WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            }

            $_SESSION['success_message'] = "تم تحديث إعدادات الموقع بنجاح";

            // إنشاء ملف CSS مخصص للألوان
            create_custom_css($primary_color, $secondary_color);

            // إعادة توجيه المستخدم إلى نفس الصفحة لتطبيق التغييرات
            header("Location: site_settings.php");
            exit;
        }
    }
}

// الحصول على إعدادات الموقع
$settings = [];
if ($table_exists) {
    $stmt = $pdo->query("SELECT * FROM site_settings");
    $settings_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($settings_data as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
}

// دالة لإنشاء ملف CSS مخصص
function create_custom_css($primary_color, $secondary_color) {
    $css_content = "/* ملف CSS المخصص للألوان - تم إنشاؤه تلقائيًا */\n\n";
    $css_content .= ":root {\n";
    $css_content .= "    --primary-color: {$primary_color};\n";
    $css_content .= "    --secondary-color: {$secondary_color};\n";
    $css_content .= "}\n\n";

    // تنسيقات العلامة التجارية والشعار
    $css_content .= ".brand-primary {\n";
    $css_content .= "    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));\n";
    $css_content .= "    -webkit-background-clip: text;\n";
    $css_content .= "    -webkit-text-fill-color: transparent;\n";
    $css_content .= "    background-clip: text;\n";
    $css_content .= "    text-fill-color: transparent;\n";
    $css_content .= "}\n\n";

    // تنسيقات شريط التنقل
    $css_content .= ".modern-navbar {\n";
    $css_content .= "    border-bottom: 3px solid var(--primary-color);\n";
    $css_content .= "}\n\n";
    $css_content .= ".nav-icon {\n";
    $css_content .= "    color: var(--primary-color);\n";
    $css_content .= "}\n\n";
    $css_content .= ".navbar-light .navbar-nav .nav-link.active {\n";
    $css_content .= "    color: var(--primary-color);\n";
    $css_content .= "}\n\n";
    $css_content .= ".navbar-light .navbar-nav .nav-link.active .nav-icon {\n";
    $css_content .= "    color: var(--primary-color);\n";
    $css_content .= "}\n\n";



    file_put_contents("../assets/css/custom-colors.css", $css_content);
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إعدادات الموقع</h1>
</div>

<?php if (isset($_SESSION['success_message'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php
    echo $_SESSION['success_message'];
    unset($_SESSION['success_message']);
    ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php
    echo $_SESSION['error_message'];
    unset($_SESSION['error_message']);
    ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($_SESSION['warning_message'])): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <?php
    echo $_SESSION['warning_message'];
    unset($_SESSION['warning_message']);
    ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    <div class="mt-3">
        <a href="site_settings.php?create_table=true" class="btn btn-primary">
            <i class="fas fa-database me-1"></i> إنشاء جدول الإعدادات
        </a>
    </div>
</div>
<?php endif; ?>

<?php if ($table_exists): ?>
<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">إعدادات الموقع الأساسية</h5>
    </div>
    <div class="card-body">
        <form action="" method="post" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">اسم الموقع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo isset($settings['site_name']) ? $settings['site_name'] : ''; ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="site_slogan" class="form-label">شعار الموقع</label>
                        <input type="text" class="form-control" id="site_slogan" name="site_slogan" value="<?php echo isset($settings['site_slogan']) ? $settings['site_slogan'] : ''; ?>">
                    </div>
                    <div class="mb-3">
                        <label for="footer_text" class="form-label">نص التذييل</label>
                        <input type="text" class="form-control" id="footer_text" name="footer_text" value="<?php echo isset($settings['footer_text']) ? $settings['footer_text'] : ''; ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_logo" class="form-label">شعار الموقع (اللوجو)</label>
                        <input type="file" class="form-control" id="site_logo" name="site_logo">
                        <div class="form-text">يفضل رفع صورة بصيغة PNG أو SVG بخلفية شفافة</div>
                    </div>
                    <div class="mb-3">
                        <label for="logo_preview" class="form-label">الشعار الحالي</label>
                        <div class="border rounded p-3 text-center">
                            <?php if (!empty($settings['site_logo']) && file_exists($upload_dir . '/' . $settings['site_logo'])): ?>
                            <img id="logo-preview" src="../uploads/logo/<?php echo $settings['site_logo']; ?>" alt="شعار الموقع" class="img-fluid" style="max-height: 100px;">
                            <?php else: ?>
                            <div class="text-muted">لا يوجد شعار محدد</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="primary_color" class="form-label">اللون الرئيسي</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="<?php echo isset($settings['primary_color']) ? $settings['primary_color'] : '#ff7700'; ?>">
                            <input type="text" class="form-control" id="primary_color_text" value="<?php echo isset($settings['primary_color']) ? $settings['primary_color'] : '#ff7700'; ?>" readonly>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="secondary_color" class="form-label">اللون الثانوي</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" value="<?php echo isset($settings['secondary_color']) ? $settings['secondary_color'] : '#ff9933'; ?>">
                            <input type="text" class="form-control" id="secondary_color_text" value="<?php echo isset($settings['secondary_color']) ? $settings['secondary_color'] : '#ff9933'; ?>" readonly>
                        </div>
                    </div>
                </div>


                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- سكريبت معاينة الشعار وتحديث قيم الألوان -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // معاينة الشعار
        const logoInput = document.getElementById('site_logo');
        const logoPreview = document.getElementById('logo-preview');

        if (logoInput && logoPreview) {
            logoInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewContainer = logoPreview.parentElement;
                        if (previewContainer.querySelector('.text-muted')) {
                            previewContainer.innerHTML = '<img id="logo-preview" src="' + e.target.result + '" alt="شعار الموقع" class="img-fluid" style="max-height: 100px;">';
                        } else {
                            logoPreview.src = e.target.result;
                        }
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }

        // تحديث قيم الألوان
        const primaryColor = document.getElementById('primary_color');
        const primaryColorText = document.getElementById('primary_color_text');
        const secondaryColor = document.getElementById('secondary_color');
        const secondaryColorText = document.getElementById('secondary_color_text');

        if (primaryColor && primaryColorText) {
            primaryColor.addEventListener('input', function() {
                primaryColorText.value = this.value;
            });
        }

        if (secondaryColor && secondaryColorText) {
            secondaryColor.addEventListener('input', function() {
                secondaryColorText.value = this.value;
            });
        }
    });
</script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
