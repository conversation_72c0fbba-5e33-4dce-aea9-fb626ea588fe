<?php
$page_title = 'فحص جدول البكجات';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// تضمين ملف الرأس
require_once 'includes/header.php';

try {
    // فحص هيكل جدول البكجات
    $stmt = $pdo->query("DESCRIBE packages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب بيانات البكجات
    $packages_stmt = $pdo->query("SELECT * FROM packages LIMIT 5");
    $packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">فحص جدول البكجات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="packages.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى البكجات
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
<div class="alert alert-danger">
    <?php echo $error; ?>
</div>
<?php else: ?>

<!-- هيكل الجدول -->
<div class="card admin-card mb-4">
    <div class="card-header">
        <h5 class="mb-0">هيكل جدول packages</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم العمود</th>
                        <th>نوع البيانات</th>
                        <th>يمكن أن يكون NULL</th>
                        <th>المفتاح</th>
                        <th>القيمة الافتراضية</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($columns as $column): ?>
                    <tr>
                        <td><strong><?php echo $column['Field']; ?></strong></td>
                        <td><?php echo $column['Type']; ?></td>
                        <td><?php echo $column['Null']; ?></td>
                        <td><?php echo $column['Key']; ?></td>
                        <td><?php echo $column['Default']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- بيانات البكجات -->
<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">عينة من بيانات البكجات (أول 5 سجلات)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($packages)): ?>
        <div class="alert alert-info">لا توجد بكجات في قاعدة البيانات</div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>العنوان</th>
                        <th>السعر</th>
                        <th>المكونات</th>
                        <th>معرفات المنتجات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($packages as $package): ?>
                    <tr>
                        <td><?php echo $package['id']; ?></td>
                        <td><?php echo $package['title']; ?></td>
                        <td><?php echo $package['price']; ?></td>
                        <td>
                            <?php if (!empty($package['components'])): ?>
                                <pre style="font-size: 10px; max-height: 100px; overflow-y: auto;"><?php echo htmlspecialchars($package['components']); ?></pre>
                            <?php else: ?>
                                <span class="text-muted">فارغ</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if (!empty($package['product_ids'])): ?>
                                <pre style="font-size: 10px;"><?php echo htmlspecialchars($package['product_ids']); ?></pre>
                            <?php else: ?>
                                <span class="text-muted">فارغ</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php endif; ?>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
