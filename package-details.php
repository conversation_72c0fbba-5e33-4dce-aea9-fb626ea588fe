<?php
require_once 'includes/header.php';

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = $_GET['id'];

// استعلام للحصول على تفاصيل البكج
$package_stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$package_stmt->execute([$package_id]);
$package = $package_stmt->fetch(PDO::FETCH_ASSOC);

// التحقق من وجود البكج
if (!$package) {
    header('Location: packages.php');
    exit;
}

// الحصول على تفاصيل مكونات البكج
$components = isset($package['components']) ? json_decode($package['components'], true) : null;
?>

<!-- تفاصيل البكج -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mb-4">
                <img src="uploads/packages/<?php echo $package['image']; ?>" class="img-fluid rounded shadow" alt="<?php echo $package['title']; ?>">
            </div>
            <div class="col-lg-6">
                <h1><?php echo $package['title']; ?></h1>
                <p class="lead"><?php echo $package['description']; ?></p>
                <div class="d-flex align-items-center mb-4">
                    <h3 class="price mb-0 me-3"><?php echo number_format($package['price']); ?> د.ع</h3>
                    <span class="badge bg-success">متوفر</span>
                </div>
                <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن بكج ' . $package['title'] . ' بسعر ' . number_format($package['price']) . ' د.ع'); ?>" class="btn btn-success btn-lg mb-3" target="_blank">
                    <i class="fab fa-whatsapp"></i> طلب عبر واتساب
                </a>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> يشمل السعر التوصيل والتركيب داخل النجف
                </div>
            </div>
        </div>

        <!-- تفاصيل مكونات البكج -->
        <div class="mt-5">
            <h3 class="mb-4">تفاصيل مكونات البكج</h3>

            <?php if (!empty($components)): ?>
            <div class="row">
                <!-- الألواح الشمسية -->
                <?php if (!empty($components['panel']['name']) || !empty($components['panel']['count']) || !empty($components['panel']['power'])): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-solar-panel me-2"></i> الألواح الشمسية</h5>
                        </div>
                        <?php if (!empty($components['panel']['image'])): ?>
                        <img src="uploads/products/<?php echo $components['panel']['image']; ?>" class="card-img-top" alt="الألواح الشمسية" style="height: 200px; object-fit: contain; padding: 10px;">
                        <?php endif; ?>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <?php if (!empty($components['panel']['name'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-tag me-2 text-primary"></i> النوع</span>
                                    <span class="fw-bold"><?php echo $components['panel']['name']; ?></span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['panel']['count'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-hashtag me-2 text-primary"></i> العدد</span>
                                    <span class="fw-bold"><?php echo $components['panel']['count']; ?> لوح</span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['panel']['power'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-bolt me-2 text-primary"></i> القدرة</span>
                                    <span class="fw-bold"><?php echo $components['panel']['power']; ?> واط</span>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- بطاريات الليثيوم -->
                <?php if (!empty($components['battery']['name']) || !empty($components['battery']['count']) || !empty($components['battery']['power'])): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-car-battery me-2"></i> بطاريات الليثيوم</h5>
                        </div>
                        <?php if (!empty($components['battery']['image'])): ?>
                        <img src="uploads/products/<?php echo $components['battery']['image']; ?>" class="card-img-top" alt="بطاريات الليثيوم" style="height: 200px; object-fit: contain; padding: 10px;">
                        <?php endif; ?>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <?php if (!empty($components['battery']['name'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-tag me-2 text-warning"></i> النوع</span>
                                    <span class="fw-bold"><?php echo $components['battery']['name']; ?></span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['battery']['count'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-hashtag me-2 text-warning"></i> العدد</span>
                                    <span class="fw-bold"><?php echo $components['battery']['count']; ?> بطارية</span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['battery']['power'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-bolt me-2 text-warning"></i> القدرة</span>
                                    <span class="fw-bold"><?php echo $components['battery']['power']; ?> أمبير</span>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- الإنفرترات -->
                <?php if (!empty($components['inverter']['name']) || !empty($components['inverter']['count']) || !empty($components['inverter']['power'])): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-plug me-2"></i> الإنفرترات</h5>
                        </div>
                        <?php if (!empty($components['inverter']['image'])): ?>
                        <img src="uploads/products/<?php echo $components['inverter']['image']; ?>" class="card-img-top" alt="الإنفرترات" style="height: 200px; object-fit: contain; padding: 10px;">
                        <?php endif; ?>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <?php if (!empty($components['inverter']['name'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-tag me-2 text-info"></i> النوع</span>
                                    <span class="fw-bold"><?php echo $components['inverter']['name']; ?></span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['inverter']['count'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-hashtag me-2 text-info"></i> العدد</span>
                                    <span class="fw-bold"><?php echo $components['inverter']['count']; ?> إنفرتر</span>
                                </li>
                                <?php endif; ?>

                                <?php if (!empty($components['inverter']['power'])): ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-bolt me-2 text-info"></i> القدرة</span>
                                    <span class="fw-bold"><?php echo $components['inverter']['power']; ?> واط</span>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد تفاصيل إضافية متاحة عن مكونات هذا البكج.
            </div>
            <?php endif; ?>
        </div>



        <!-- معلومات إضافية -->
        <div class="mt-5">
            <h3 class="mb-4">معلومات إضافية</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">المواصفات الفنية</h5>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li>قدرة النظام: حسب احتياجات العميل</li>
                                <li>وقت التشغيل: 24 ساعة (حسب الاستهلاك)</li>
                                <li>ضمان الألواح الشمسية: 25 سنة</li>
                                <li>ضمان الإنفرتر: 5 سنوات</li>
                                <li>ضمان البطاريات: 2-5 سنوات (حسب النوع)</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">خدمات ما بعد البيع</h5>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li>تركيب مجاني داخل بغداد</li>
                                <li>صيانة دورية</li>
                                <li>دعم فني على مدار الساعة</li>
                                <li>إمكانية ترقية النظام مستقبلاً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
