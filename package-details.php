<?php
require_once 'includes/header.php';

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = $_GET['id'];

// استعلام للحصول على تفاصيل البكج
$package_stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$package_stmt->execute([$package_id]);
$package = $package_stmt->fetch(PDO::FETCH_ASSOC);

// التحقق من وجود البكج
if (!$package) {
    header('Location: packages.php');
    exit;
}

// الحصول على تفاصيل مكونات البكج
$components = isset($package['components']) ? json_decode($package['components'], true) : null;

// حساب القدرة الإجمالية
$total_power = 0;
if (!empty($components['panel']['count']) && !empty($components['panel']['power'])) {
    $panel_power = (int)$components['panel']['power'];
    $panel_count = (int)$components['panel']['count'];
    $total_power = $panel_power * $panel_count;
}

// جلب البكجات المشابهة
$similar_stmt = $pdo->prepare("SELECT * FROM packages WHERE id != ? ORDER BY RAND() LIMIT 3");
$similar_stmt->execute([$package_id]);
$similar_packages = $similar_stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب معلومات البكجات الإضافية
$technical_stmt = $pdo->query("SELECT * FROM package_additional_info WHERE section_type = 'technical' AND is_active = 1 ORDER BY display_order");
$technical_info = $technical_stmt->fetchAll(PDO::FETCH_ASSOC);

$services_stmt = $pdo->query("SELECT * FROM package_additional_info WHERE section_type = 'services' AND is_active = 1 ORDER BY display_order");
$services_info = $services_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="packages.php">البكجات</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo $package['title']; ?></li>
        </ol>
    </div>
</nav>

<!-- تفاصيل البكج -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="position-relative">
                    <img src="uploads/products/<?php echo $package['image']; ?>" class="img-fluid rounded shadow-lg" alt="<?php echo $package['title']; ?>" style="width: 100%; height: 400px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-success fs-6 px-3 py-2">
                            <i class="fas fa-check-circle me-1"></i> متوفر
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="mb-3">
                    <span class="badge bg-primary fs-6 px-3 py-2 mb-3">
                        <i class="fas fa-solar-panel me-1"></i> بكج طاقة شمسية
                    </span>
                </div>

                <h1 class="display-5 fw-bold text-primary mb-3"><?php echo $package['title']; ?></h1>

                <div class="mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-4">
                            <h2 class="price text-success mb-0 fw-bold"><?php echo number_format($package['price']); ?> <small class="text-muted fs-5">د.ع</small></h2>
                        </div>
                        <?php if ($total_power > 0): ?>
                        <div class="text-center">
                            <div class="badge bg-warning text-dark fs-6 px-3 py-2">
                                <i class="fas fa-bolt me-1"></i> <?php echo number_format($total_power); ?> واط
                            </div>
                            <small class="d-block text-muted mt-1">القدرة الإجمالية</small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="text-secondary mb-3">وصف البكج:</h5>
                    <p class="lead text-muted"><?php echo nl2br($package['description']); ?></p>
                </div>

                <!-- مميزات سريعة -->
                <div class="row mb-4">
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shipping-fast text-success me-2"></i>
                            <small class="text-muted">توصيل مجاني</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-tools text-primary me-2"></i>
                            <small class="text-muted">تركيب مجاني</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shield-alt text-warning me-2"></i>
                            <small class="text-muted">ضمان شامل</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-headset text-info me-2"></i>
                            <small class="text-muted">دعم فني 24/7</small>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 mb-4">
                    <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن بكج ' . $package['title'] . ' بسعر ' . number_format($package['price']) . ' د.ع'); ?>" class="btn btn-success btn-lg" target="_blank">
                        <i class="fab fa-whatsapp me-2"></i> استفسار عبر واتساب
                    </a>
                    <a href="tel:<?php echo $company_info['phone']; ?>" class="btn btn-outline-primary">
                        <i class="fas fa-phone me-2"></i> اتصل بنا الآن
                    </a>
                </div>

                <div class="alert alert-info border-0 bg-light">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle text-info me-3 fs-4"></i>
                        <div>
                            <strong>ملاحظة مهمة:</strong><br>
                            <small class="text-muted">يشمل السعر التوصيل والتركيب داخل النجف. للمناطق الأخرى يرجى التواصل معنا.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل مكونات البكج -->
        <div class="mt-5">
            <div class="text-center mb-5">
                <h2 class="display-6 fw-bold text-primary mb-3">مكونات البكج</h2>
                <p class="lead text-muted">تفاصيل شاملة عن جميع مكونات النظام</p>
                <div class="mx-auto" style="width: 100px; height: 3px; background: linear-gradient(90deg, #007bff, #28a745, #ffc107);"></div>
            </div>

            <?php if (!empty($components)): ?>
            <div class="row g-4">
                <!-- الألواح الشمسية -->
                <?php if (!empty($components['panel']['name']) || !empty($components['panel']['count']) || !empty($components['panel']['power'])): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg component-card" style="transition: transform 0.3s ease;">
                        <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <div class="mb-2">
                                <i class="fas fa-solar-panel fa-3x"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">الألواح الشمسية</h5>
                        </div>
                        <?php if (!empty($components['panel']['image'])): ?>
                        <div class="position-relative overflow-hidden">
                            <img src="uploads/products/<?php echo $components['panel']['image']; ?>" class="card-img-top" alt="الألواح الشمسية" style="height: 220px; object-fit: cover; transition: transform 0.3s ease;">
                        </div>
                        <?php endif; ?>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <?php if (!empty($components['panel']['name'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-tag text-primary fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <small class="text-muted d-block">النوع</small>
                                            <span class="fw-bold text-dark"><?php echo $components['panel']['name']; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['panel']['count'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                        <i class="fas fa-hashtag text-primary fs-4 mb-2"></i>
                                        <div class="fw-bold text-primary fs-5"><?php echo $components['panel']['count']; ?></div>
                                        <small class="text-muted">لوح</small>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['panel']['power'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <i class="fas fa-bolt text-warning fs-4 mb-2"></i>
                                        <div class="fw-bold text-warning fs-5"><?php echo $components['panel']['power']; ?></div>
                                        <small class="text-muted">واط</small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- بطاريات الليثيوم -->
                <?php if (!empty($components['battery']['name']) || !empty($components['battery']['count']) || !empty($components['battery']['power'])): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg component-card" style="transition: transform 0.3s ease;">
                        <div class="card-header bg-gradient text-dark text-center py-4" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                            <div class="mb-2">
                                <i class="fas fa-car-battery fa-3x"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">بطاريات الليثيوم</h5>
                        </div>
                        <?php if (!empty($components['battery']['image'])): ?>
                        <div class="position-relative overflow-hidden">
                            <img src="uploads/products/<?php echo $components['battery']['image']; ?>" class="card-img-top" alt="بطاريات الليثيوم" style="height: 220px; object-fit: cover; transition: transform 0.3s ease;">
                        </div>
                        <?php endif; ?>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <?php if (!empty($components['battery']['name'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-tag text-warning fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <small class="text-muted d-block">النوع</small>
                                            <span class="fw-bold text-dark"><?php echo $components['battery']['name']; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['battery']['count'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <i class="fas fa-hashtag text-warning fs-4 mb-2"></i>
                                        <div class="fw-bold text-warning fs-5"><?php echo $components['battery']['count']; ?></div>
                                        <small class="text-muted">بطارية</small>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['battery']['power'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                        <i class="fas fa-bolt text-success fs-4 mb-2"></i>
                                        <div class="fw-bold text-success fs-5"><?php echo $components['battery']['power']; ?></div>
                                        <small class="text-muted">أمبير</small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- الإنفرترات -->
                <?php if (!empty($components['inverter']['name']) || !empty($components['inverter']['count']) || !empty($components['inverter']['power'])): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-lg component-card" style="transition: transform 0.3s ease;">
                        <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                            <div class="mb-2">
                                <i class="fas fa-plug fa-3x"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">الإنفرترات</h5>
                        </div>
                        <?php if (!empty($components['inverter']['image'])): ?>
                        <div class="position-relative overflow-hidden">
                            <img src="uploads/products/<?php echo $components['inverter']['image']; ?>" class="card-img-top" alt="الإنفرترات" style="height: 220px; object-fit: cover; transition: transform 0.3s ease;">
                        </div>
                        <?php endif; ?>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <?php if (!empty($components['inverter']['name'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-tag text-info fs-4"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <small class="text-muted d-block">النوع</small>
                                            <span class="fw-bold text-dark"><?php echo $components['inverter']['name']; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['inverter']['count'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                        <i class="fas fa-hashtag text-info fs-4 mb-2"></i>
                                        <div class="fw-bold text-info fs-5"><?php echo $components['inverter']['count']; ?></div>
                                        <small class="text-muted">إنفرتر</small>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($components['inverter']['power'])): ?>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-danger bg-opacity-10 rounded">
                                        <i class="fas fa-bolt text-danger fs-4 mb-2"></i>
                                        <div class="fw-bold text-danger fs-5"><?php echo $components['inverter']['power']; ?></div>
                                        <small class="text-muted">واط</small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-info-circle text-muted" style="font-size: 4rem;"></i>
                </div>
                <h4 class="text-muted mb-3">لا توجد تفاصيل مكونات</h4>
                <p class="text-muted">لا توجد تفاصيل إضافية متاحة عن مكونات هذا البكج حالياً.</p>
            </div>
            <?php endif; ?>
        </div>



        <!-- معلومات إضافية -->
        <div class="mt-5">
            <div class="text-center mb-5">
                <h2 class="display-6 fw-bold text-primary mb-3">معلومات إضافية</h2>
                <p class="lead text-muted">تفاصيل فنية وخدمات شاملة</p>
                <div class="mx-auto" style="width: 100px; height: 3px; background: linear-gradient(90deg, #007bff, #28a745);"></div>
            </div>

            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-lg h-100">
                        <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                            <i class="fas fa-cogs fa-2x mb-2"></i>
                            <h5 class="mb-0 fw-bold">المواصفات الفنية</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <?php if (!empty($technical_info)): ?>
                                    <?php foreach ($technical_info as $tech): ?>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center p-3 bg-light rounded">
                                            <i class="<?php echo $tech['icon']; ?> text-primary fs-4 me-3"></i>
                                            <div>
                                                <strong><?php echo htmlspecialchars($tech['title']); ?>:</strong><br>
                                                <small class="text-muted"><?php echo nl2br(htmlspecialchars($tech['description'])); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="text-center py-4">
                                            <i class="fas fa-info-circle text-muted fs-1 mb-3"></i>
                                            <p class="text-muted">لا توجد مواصفات فنية متاحة حالياً.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card border-0 shadow-lg h-100">
                        <div class="card-header bg-gradient text-white text-center py-4" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                            <i class="fas fa-handshake fa-2x mb-2"></i>
                            <h5 class="mb-0 fw-bold">خدمات ما بعد البيع</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row g-3">
                                <?php if (!empty($services_info)): ?>
                                    <?php foreach ($services_info as $service): ?>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center p-3 bg-light rounded">
                                            <i class="<?php echo $service['icon']; ?> text-success fs-4 me-3"></i>
                                            <div>
                                                <strong><?php echo htmlspecialchars($service['title']); ?></strong><br>
                                                <small class="text-muted"><?php echo nl2br(htmlspecialchars($service['description'])); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="text-center py-4">
                                            <i class="fas fa-info-circle text-muted fs-1 mb-3"></i>
                                            <p class="text-muted">لا توجد خدمات متاحة حالياً.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- البكجات المشابهة -->
        <?php if (!empty($similar_packages)): ?>
        <div class="mt-5">
            <div class="text-center mb-5">
                <h2 class="display-6 fw-bold text-primary mb-3">بكجات مشابهة</h2>
                <p class="lead text-muted">قد تهمك هذه البكجات أيضاً</p>
                <div class="mx-auto" style="width: 100px; height: 3px; background: linear-gradient(90deg, #007bff, #ffc107);"></div>
            </div>

            <div class="row g-4">
                <?php foreach ($similar_packages as $similar): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-lg h-100" style="transition: transform 0.3s ease;">
                        <div class="position-relative overflow-hidden">
                            <img src="uploads/products/<?php echo $similar['image']; ?>" class="card-img-top" alt="<?php echo $similar['title']; ?>" style="height: 200px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-success">متوفر</span>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <h5 class="card-title fw-bold mb-3"><?php echo $similar['title']; ?></h5>
                            <p class="card-text text-muted mb-3"><?php echo substr($similar['description'], 0, 100) . '...'; ?></p>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="h5 text-success mb-0 fw-bold"><?php echo number_format($similar['price']); ?> د.ع</span>
                            </div>
                            <div class="d-grid">
                                <a href="package-details.php?id=<?php echo $similar['id']; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- CSS إضافي للتحسينات -->
<style>
.component-card:hover {
    transform: translateY(-10px);
}

.component-card img:hover {
    transform: scale(1.05);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
}

@media (max-width: 768px) {
    .display-5 {
        font-size: 2rem;
    }

    .display-6 {
        font-size: 1.5rem;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
