<?php
$page_title = 'تعديل معلومة البكج';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التحقق من وجود معرف المعلومة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: package_additional_info.php');
    exit;
}

$info_id = (int)$_GET['id'];

// جلب بيانات المعلومة
$stmt = $pdo->prepare("SELECT * FROM package_additional_info WHERE id = ?");
$stmt->execute([$info_id]);
$info = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$info) {
    header('Location: package_additional_info.php');
    exit;
}

$success = '';
$error = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $section_title = trim($_POST['section_title'] ?? '');
    $section_type = trim($_POST['section_type'] ?? '');
    $icon = trim($_POST['icon'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $display_order = (int)($_POST['display_order'] ?? 0);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // التحقق من البيانات
    if (empty($section_title) || empty($section_type) || empty($icon) || empty($title) || empty($description)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE package_additional_info SET section_title = ?, section_type = ?, icon = ?, title = ?, description = ?, display_order = ?, is_active = ? WHERE id = ?");
            $result = $stmt->execute([$section_title, $section_type, $icon, $title, $description, $display_order, $is_active, $info_id]);
            
            if ($result) {
                $success = 'تم تحديث المعلومة بنجاح!';
                // إعادة جلب البيانات المحدثة
                $stmt = $pdo->prepare("SELECT * FROM package_additional_info WHERE id = ?");
                $stmt->execute([$info_id]);
                $info = $stmt->fetch(PDO::FETCH_ASSOC);
            } else {
                $error = 'حدث خطأ أثناء تحديث المعلومة';
            }
        } catch (Exception $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل معلومة البكج</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="package_additional_info.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى القائمة
        </a>
    </div>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">تعديل: <?php echo htmlspecialchars($info['title']); ?></h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="section_type" class="form-label">نوع القسم <span class="text-danger">*</span></label>
                    <select class="form-select" id="section_type" name="section_type" required onchange="updateSectionTitle()">
                        <option value="">اختر نوع القسم</option>
                        <option value="technical" <?php echo ($info['section_type'] === 'technical') ? 'selected' : ''; ?>>المواصفات الفنية</option>
                        <option value="services" <?php echo ($info['section_type'] === 'services') ? 'selected' : ''; ?>>خدمات ما بعد البيع</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="section_title" class="form-label">عنوان القسم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="section_title" name="section_title" value="<?php echo htmlspecialchars($info['section_title']); ?>" required readonly>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="title" class="form-label">العنوان <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($info['title']); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="icon" class="form-label">الأيقونة <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i id="icon-preview" class="<?php echo $info['icon']; ?>"></i></span>
                        <input type="text" class="form-control" id="icon" name="icon" value="<?php echo htmlspecialchars($info['icon']); ?>" placeholder="fas fa-bolt" required onkeyup="updateIconPreview()">
                    </div>
                    <small class="form-text text-muted">استخدم أيقونات FontAwesome مثل: fas fa-bolt, fas fa-clock, fas fa-shield-alt</small>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($info['description']); ?></textarea>
                <small class="form-text text-muted">يمكنك استخدام أسطر متعددة. استخدم • للنقاط</small>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="display_order" class="form-label">ترتيب العرض</label>
                    <input type="number" class="form-control" id="display_order" name="display_order" value="<?php echo $info['display_order']; ?>" min="0">
                    <small class="form-text text-muted">الرقم الأصغر يظهر أولاً</small>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $info['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">
                            نشط (سيظهر في الموقع)
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="package_additional_info.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function updateSectionTitle() {
    const sectionType = document.getElementById('section_type').value;
    const sectionTitle = document.getElementById('section_title');
    
    if (sectionType === 'technical') {
        sectionTitle.value = 'المواصفات الفنية';
    } else if (sectionType === 'services') {
        sectionTitle.value = 'خدمات ما بعد البيع';
    } else {
        sectionTitle.value = '';
    }
}

function updateIconPreview() {
    const iconInput = document.getElementById('icon').value;
    const iconPreview = document.getElementById('icon-preview');
    
    if (iconInput) {
        iconPreview.className = iconInput;
    } else {
        iconPreview.className = 'fas fa-question';
    }
}
</script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
