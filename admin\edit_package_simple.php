<?php
$page_title = 'تعديل البكج - مبسط';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = (int)$_GET['id'];

// جلب بيانات البكج
$stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$stmt->execute([$package_id]);
$package = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$package) {
    header('Location: packages.php');
    exit;
}

$success = '';
$error = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $price = $_POST['price'] ?? '';
    
    // التحقق البسيط
    if (empty($title) || empty($description) || empty($price)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!is_numeric($price) || $price <= 0) {
        $error = 'يرجى إدخال سعر صحيح';
    } else {
        // تحديث البكج
        try {
            $stmt = $pdo->prepare("UPDATE packages SET title = ?, description = ?, price = ? WHERE id = ?");
            $result = $stmt->execute([$title, $description, $price, $package_id]);
            
            if ($result) {
                $affected_rows = $stmt->rowCount();
                if ($affected_rows > 0) {
                    $success = 'تم تحديث البكج بنجاح!';
                    // إعادة جلب البيانات المحدثة
                    $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
                    $stmt->execute([$package_id]);
                    $package = $stmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    $error = 'لم يتم إجراء أي تغييرات على البيانات';
                }
            } else {
                $error = 'حدث خطأ أثناء تحديث البكج';
            }
        } catch (Exception $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل البكج (مبسط)</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="packages.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى البكجات
        </a>
        <a href="edit_package.php?id=<?php echo $package_id; ?>" class="btn btn-sm btn-info ms-2">
            <i class="fas fa-cogs"></i> النسخة المتقدمة
        </a>
    </div>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">تعديل البكج: <?php echo htmlspecialchars($package['title']); ?></h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="title" class="form-label">عنوان البكج <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($package['title']); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $package['price']; ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف البكج <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($package['description']); ?></textarea>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> هذه النسخة المبسطة تسمح بتعديل العنوان والوصف والسعر فقط. 
                لتعديل الصور والمكونات، استخدم النسخة المتقدمة.
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="packages.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<div class="card admin-card mt-4">
    <div class="card-header">
        <h5 class="mb-0">معلومات البكج الحالية</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>العنوان:</strong> <?php echo htmlspecialchars($package['title']); ?></p>
                <p><strong>السعر:</strong> <?php echo number_format($package['price'], 2); ?> د.ع</p>
            </div>
            <div class="col-md-6">
                <p><strong>تاريخ الإنشاء:</strong> <?php echo $package['created_at']; ?></p>
                <p><strong>آخر تحديث:</strong> <?php echo $package['updated_at'] ?? 'غير محدد'; ?></p>
            </div>
        </div>
        <div class="mt-3">
            <p><strong>الوصف:</strong></p>
            <p><?php echo nl2br(htmlspecialchars($package['description'])); ?></p>
        </div>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
