<?php
require_once 'includes/header.php';

// استعلام للحصول على الإكسسوارات
$accessories_stmt = $pdo->query("SELECT * FROM products WHERE category = 'accessories' ORDER BY id DESC");
$accessories = $accessories_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-5">
    <div class="container">
        <h1 class="text-center">إكسسوارات الطاقة الشمسية</h1>
        <p class="text-center lead">مجموعة متنوعة من إكسسوارات الطاقة الشمسية عالية الجودة</p>
    </div>
</div>

<!-- قائمة الإكسسوارات -->
<section class="py-5">
    <div class="container">
        <?php if (empty($accessories)): ?>
        <div class="alert alert-info text-center">
            لا توجد إكسسوارات متاحة حالياً. يرجى التحقق لاحقاً.
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($accessories as $accessory): ?>
            <div class="col-md-4 mb-4 animate-on-scroll">
                <div class="card product-card h-100">
                    <img src="uploads/products_images/<?php echo $accessory['image']; ?>" class="card-img-top" alt="<?php echo $accessory['name']; ?>" style="height: 200px; object-fit: contain; background-color: #f8f9fa;">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo $accessory['name']; ?></h5>
                        <p class="card-text"><?php echo substr($accessory['description'], 0, 80); ?>...</p>
                        <p class="price"><?php echo number_format($accessory['price']); ?> د.ع</p>
                        <div class="d-grid gap-2">
                            <a href="product-details.php?id=<?php echo $accessory['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-info-circle"></i> عرض التفاصيل
                            </a>
                            <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن ' . $accessory['name']); ?>" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp"></i> استفسار عبر واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
