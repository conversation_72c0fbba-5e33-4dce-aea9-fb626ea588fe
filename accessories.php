<?php
require_once 'includes/header.php';

// استعلام للحصول على الإكسسوارات
$accessories_stmt = $pdo->query("SELECT * FROM products WHERE category = 'accessory' ORDER BY id DESC");
$accessories = $accessories_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold text-primary mb-2">
                    <i class="fas fa-tools me-3"></i>الإكسسوارات
                </h1>
                <p class="lead text-muted">مجموعة متنوعة من إكسسوارات الطاقة الشمسية عالية الجودة</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="all_products.php" class="btn btn-outline-primary">
                    <i class="fas fa-th-large me-2"></i>جميع المنتجات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- معلومات سريعة -->
<section class="py-4 bg-primary bg-opacity-10">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-link text-warning fs-2 me-2"></i>
                    <div>
                        <h6 class="mb-0">ربط محكم</h6>
                        <small class="text-muted">اتصالات آمنة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-shield-alt text-success fs-2 me-2"></i>
                    <div>
                        <h6 class="mb-0">حماية فائقة</h6>
                        <small class="text-muted">ضد العوامل الجوية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-wrench text-info fs-2 me-2"></i>
                    <div>
                        <h6 class="mb-0">سهولة التركيب</h6>
                        <small class="text-muted">أدوات متخصصة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-star text-primary fs-2 me-2"></i>
                    <div>
                        <h6 class="mb-0">جودة عالية</h6>
                        <small class="text-muted">مواد متينة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قائمة الإكسسوارات -->
<section class="py-5">
    <div class="container">
        <?php if (empty($accessories)): ?>
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle me-2"></i>
            لا توجد إكسسوارات متاحة حالياً. يرجى التحقق لاحقاً.
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($accessories as $accessory): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card product-card h-100 border-0 shadow-lg" style="transition: transform 0.3s ease;">
                    <!-- صورة المنتج المرنة -->
                    <div class="position-relative product-image-container">
                        <img src="uploads/products_images/<?php echo $accessory['image']; ?>" class="card-img-top product-image" alt="<?php echo htmlspecialchars($accessory['name']); ?>">
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-success">متوفر</span>
                        </div>
                    </div>
                    <div class="card-body p-3">
                        <h5 class="card-title fw-bold text-primary mb-2"><?php echo htmlspecialchars($accessory['name']); ?></h5>
                        <p class="card-text text-muted mb-3"><?php echo substr($accessory['description'], 0, 80); ?>...</p>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="h5 text-success mb-0 fw-bold"><?php echo number_format($accessory['price']); ?> د.ع</span>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="product-details.php?id=<?php echo $accessory['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i> عرض التفاصيل
                            </a>
                            <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن ' . $accessory['name']); ?>" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-1"></i> استفسار عبر واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- تحسين مظهر المنتجات -->
<style>
/* تحسين عرض المنتجات */
.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.product-card img:hover {
    transform: scale(1.02);
}

/* تحسين عرض صور المنتجات */
.product-image-container {
    background: #f8f9fa;
    border-radius: 0.375rem 0.375rem 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    transition: transform 0.3s ease;
    padding: 15px;
    background: transparent;
}

/* تحسين الأزرار */
.btn {
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

/* تحسينات الموبايل */
@media (max-width: 768px) {
    .product-image-container {
        min-height: 180px;
    }

    .product-image {
        padding: 10px;
    }
}

/* تحسين إضافي للصور */
.product-image {
    /* إزالة أي قيود على الارتفاع */
    max-height: none !important;
    /* السماح للصورة بأن تأخذ حجمها الطبيعي */
    height: auto !important;
    /* ضمان عدم تشويه الصورة */
    object-fit: contain !important;
}
</style>

<?php require_once 'includes/footer.php'; ?>
