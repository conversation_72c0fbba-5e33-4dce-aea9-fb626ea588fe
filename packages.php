<?php
require_once 'includes/header.php';

// استعلام للحصول على البكجات
$packages_stmt = $pdo->query("SELECT * FROM packages ORDER BY id DESC");
$packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة بيانات المكونات لكل بكج
foreach ($packages as &$package) {
    $package['components'] = isset($package['components']) ? json_decode($package['components'], true) : null;

    // حساب القدرة الإجمالية
    $package['total_power'] = 0;
    if (!empty($package['components']['panel']['count']) && !empty($package['components']['panel']['power'])) {
        $panel_power = (int)$package['components']['panel']['power'];
        $panel_count = (int)$package['components']['panel']['count'];
        $package['total_power'] = $panel_power * $panel_count;
    }
}
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-5">
    <div class="container">
        <h1 class="text-center">البكجات الجاهزة</h1>
        <p class="text-center lead">حلول متكاملة للطاقة الشمسية بأسعار منافسة</p>
    </div>
</div>

<!-- قسم البكجات -->
<section class="py-5">
    <div class="container">
        <?php if (empty($packages)): ?>
        <div class="text-center py-5">
            <i class="fas fa-box-open text-muted" style="font-size: 4rem;"></i>
            <h3 class="text-muted mt-3">لا توجد بكجات متاحة حالياً</h3>
            <p class="text-muted">يرجى التحقق لاحقاً أو التواصل معنا للاستفسار</p>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($packages as $package): ?>
            <div class="col-lg-6 col-xl-4">
                <div class="card package-card border-0 shadow-lg h-100" style="transition: transform 0.3s ease, box-shadow 0.3s ease;">
                    <!-- صورة البكج الكاملة -->
                    <div class="position-relative overflow-hidden">
                        <img src="uploads/products/<?php echo $package['image']; ?>" class="card-img-top" alt="<?php echo $package['title']; ?>" style="height: 280px; object-fit: cover; transition: transform 0.3s ease;">

                        <!-- شارة السعر -->
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-success fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-tag me-1"></i> <?php echo number_format($package['price']); ?> د.ع
                            </span>
                        </div>

                        <!-- شارة القدرة الإجمالية -->
                        <?php if ($package['total_power'] > 0): ?>
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-warning text-dark fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-bolt me-1"></i> <?php echo number_format($package['total_power']); ?> واط
                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- شارة متوفر -->
                        <div class="position-absolute bottom-0 end-0 m-3">
                            <span class="badge bg-primary fs-6 px-3 py-2 rounded-pill">
                                <i class="fas fa-check-circle me-1"></i> متوفر
                            </span>
                        </div>
                    </div>

                    <!-- محتوى البطاقة -->
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <h4 class="card-title fw-bold text-primary mb-2"><?php echo $package['title']; ?></h4>
                            <p class="card-text text-muted mb-3"><?php echo substr($package['description'], 0, 120); ?>...</p>
                        </div>

                        <!-- تفاصيل المكونات -->
                        <?php if (!empty($package['components'])): ?>
                        <div class="components-summary mb-4">
                            <h6 class="fw-bold text-secondary mb-3">
                                <i class="fas fa-cogs me-2"></i> مكونات البكج:
                            </h6>

                            <div class="row g-2">
                                <!-- الألواح الشمسية -->
                                <?php if (!empty($package['components']['panel']['name']) || !empty($package['components']['panel']['count'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-2 bg-primary bg-opacity-10 rounded">
                                        <i class="fas fa-solar-panel text-primary me-2"></i>
                                        <div class="flex-grow-1">
                                            <small class="fw-bold text-primary">الألواح الشمسية</small>
                                            <div class="text-muted small">
                                                <?php if (!empty($package['components']['panel']['name'])): ?>
                                                    <?php echo $package['components']['panel']['name']; ?>
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['panel']['count'])): ?>
                                                    - <?php echo $package['components']['panel']['count']; ?> لوح
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['panel']['power'])): ?>
                                                    - <?php echo $package['components']['panel']['power']; ?> واط
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- البطاريات -->
                                <?php if (!empty($package['components']['battery']['name']) || !empty($package['components']['battery']['count'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-2 bg-warning bg-opacity-10 rounded">
                                        <i class="fas fa-car-battery text-warning me-2"></i>
                                        <div class="flex-grow-1">
                                            <small class="fw-bold text-warning">البطاريات</small>
                                            <div class="text-muted small">
                                                <?php if (!empty($package['components']['battery']['name'])): ?>
                                                    <?php echo $package['components']['battery']['name']; ?>
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['battery']['count'])): ?>
                                                    - <?php echo $package['components']['battery']['count']; ?> بطارية
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['battery']['power'])): ?>
                                                    - <?php echo $package['components']['battery']['power']; ?> أمبير
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- الإنفرترات -->
                                <?php if (!empty($package['components']['inverter']['name']) || !empty($package['components']['inverter']['count'])): ?>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-2 bg-info bg-opacity-10 rounded">
                                        <i class="fas fa-plug text-info me-2"></i>
                                        <div class="flex-grow-1">
                                            <small class="fw-bold text-info">الإنفرترات</small>
                                            <div class="text-muted small">
                                                <?php if (!empty($package['components']['inverter']['name'])): ?>
                                                    <?php echo $package['components']['inverter']['name']; ?>
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['inverter']['count'])): ?>
                                                    - <?php echo $package['components']['inverter']['count']; ?> إنفرتر
                                                <?php endif; ?>
                                                <?php if (!empty($package['components']['inverter']['power'])): ?>
                                                    - <?php echo $package['components']['inverter']['power']; ?> واط
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- الأزرار -->
                        <div class="d-grid gap-2">
                            <a href="package-details.php?id=<?php echo $package['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i> عرض التفاصيل الكاملة
                            </a>
                            <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن بكج ' . $package['title'] . ' بسعر ' . number_format($package['price']) . ' د.ع'); ?>" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i> استفسار عبر واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- قسم المميزات -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">لماذا تختار بكجاتنا؟</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle text-primary fa-3x mb-3"></i>
                        <h4>منتجات عالية الجودة</h4>
                        <p>نختار بعناية أفضل المنتجات من الشركات العالمية المعروفة لضمان الجودة والأداء.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-tools text-primary fa-3x mb-3"></i>
                        <h4>تركيب احترافي</h4>
                        <p>فريق فني متخصص يقوم بتركيب النظام بشكل احترافي وآمن لضمان أفضل أداء.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-headset text-primary fa-3x mb-3"></i>
                        <h4>دعم فني مستمر</h4>
                        <p>نقدم خدمة ما بعد البيع وصيانة دورية لضمان استمرارية عمل النظام بكفاءة.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CSS إضافي للتحسينات -->
<style>
.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.package-card img:hover {
    transform: scale(1.05);
}

.components-summary {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
}

.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card-title {
    font-size: 1.25rem;
    line-height: 1.3;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.5;
}

.btn {
    font-weight: 600;
    border-radius: 8px;
    padding: 10px 20px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .package-card {
        margin-bottom: 2rem;
    }

    .badge {
        font-size: 0.75rem !important;
        padding: 0.5rem 1rem !important;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .components-summary {
        padding: 10px;
    }
}

/* تحسين عرض الشارات */
.position-absolute .badge {
    backdrop-filter: blur(10px);
    background-color: rgba(var(--bs-success-rgb), 0.9) !important;
}

.position-absolute .badge.bg-warning {
    background-color: rgba(var(--bs-warning-rgb), 0.9) !important;
}

.position-absolute .badge.bg-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.9) !important;
}

/* تحسين تفاصيل المكونات */
.components-summary .d-flex {
    transition: all 0.3s ease;
}

.components-summary .d-flex:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>

<?php require_once 'includes/footer.php'; ?>
