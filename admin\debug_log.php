<?php
$page_title = 'سجل التشخيص';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// تضمين ملف الرأس
require_once 'includes/header.php';

// قراءة ملف السجل
$log_file = ini_get('error_log');
if (!$log_file) {
    $log_file = '/tmp/php_errors.log'; // مسار افتراضي
}

$log_content = '';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    // عرض آخر 50 سطر فقط
    $lines = explode("\n", $log_content);
    $lines = array_slice($lines, -50);
    $log_content = implode("\n", $lines);
} else {
    $log_content = 'ملف السجل غير موجود في: ' . $log_file;
}
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">سجل التشخيص</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="packages.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى البكجات
        </a>
    </div>
</div>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">آخر 50 سطر من سجل الأخطاء</h5>
    </div>
    <div class="card-body">
        <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 500px; overflow-y: auto; font-size: 12px;"><?php echo htmlspecialchars($log_content); ?></pre>
    </div>
</div>

<div class="alert alert-info mt-3">
    <i class="fas fa-info-circle me-2"></i>
    <strong>ملاحظة:</strong> هذا السجل يعرض رسائل التشخيص من عملية تعديل البكجات. 
    قم بتعديل بكج ثم ارجع إلى هذه الصفحة لرؤية ما حدث.
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
