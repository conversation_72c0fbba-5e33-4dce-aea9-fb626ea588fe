<?php
require_once 'includes/header.php';

// استعلام للحصول على جميع المنتجات
$products_stmt = $pdo->query("SELECT * FROM products ORDER BY category, id DESC");
$products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);

// تجميع المنتجات حسب الفئة
$categorized_products = [
    'panel' => [],
    'inverter' => [],
    'battery' => [],
    'accessory' => [], // إضافة accessory بدلاً من accessories
    'accessories' => []
];

foreach ($products as $product) {
    $category = $product['category'];
    // التحقق من وجود الفئة في المصفوفة
    if (isset($categorized_products[$category])) {
        $categorized_products[$category][] = $product;
    } else {
        // إذا كانت الفئة غير معرفة، أضفها إلى accessories
        $categorized_products['accessories'][] = $product;
    }
}

// أسماء الفئات بالعربية
$category_names = [
    'panel' => 'الألواح الشمسية',
    'inverter' => 'الإنفرترات',
    'battery' => 'البطاريات',
    'accessory' => 'الإكسسوارات',
    'accessories' => 'الإكسسوارات'
];

// أيقونات الفئات
$category_icons = [
    'panel' => 'fas fa-solar-panel',
    'inverter' => 'fas fa-plug',
    'battery' => 'fas fa-car-battery',
    'accessory' => 'fas fa-tools',
    'accessories' => 'fas fa-tools'
];

// روابط الفئات
$category_links = [
    'panel' => 'panels.php',
    'inverter' => 'inverters.php',
    'battery' => 'batteries.php',
    'accessory' => 'accessories.php',
    'accessories' => 'accessories.php'
];
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-5">
    <div class="container">
        <h1 class="text-center">جميع المنتجات</h1>
        <p class="text-center lead">تصفح جميع منتجاتنا من الألواح الشمسية والإنفرترات والبطاريات والإكسسوارات</p>

        <!-- أزرار التصفية -->
        <div class="d-flex justify-content-center mt-4 filter-buttons">
            <button class="btn btn-outline-primary mx-1 active" data-filter="all">الكل</button>
            <button class="btn btn-outline-primary mx-1" data-filter="panel">
                <i class="fas fa-solar-panel"></i> الألواح الشمسية
            </button>
            <button class="btn btn-outline-primary mx-1" data-filter="inverter">
                <i class="fas fa-plug"></i> الإنفرترات
            </button>
            <button class="btn btn-outline-primary mx-1" data-filter="battery">
                <i class="fas fa-car-battery"></i> البطاريات
            </button>
            <button class="btn btn-outline-primary mx-1" data-filter="accessory">
                <i class="fas fa-tools"></i> الإكسسوارات
            </button>
        </div>
    </div>
</div>

<!-- قائمة المنتجات -->
<section class="py-5">
    <div class="container">
        <?php if (empty($products)): ?>
        <div class="alert alert-info text-center">
            لا توجد منتجات متاحة حالياً. يرجى التحقق لاحقاً.
        </div>
        <?php else: ?>
            <?php foreach ($categorized_products as $category => $category_products): ?>
                <?php if (!empty($category_products)): ?>
                <div class="product-category mb-5" id="<?php echo $category; ?>-section" data-category="<?php echo $category; ?>">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="section-title">
                            <i class="<?php echo isset($category_icons[$category]) ? $category_icons[$category] : 'fas fa-box'; ?>"></i>
                            <?php echo isset($category_names[$category]) ? $category_names[$category] : ucfirst($category); ?>
                        </h2>
                        <?php if (isset($category_links[$category])): ?>
                        <a href="<?php echo $category_links[$category]; ?>" class="btn btn-sm btn-outline-primary">
                            عرض الكل <i class="fas fa-arrow-left"></i>
                        </a>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <?php foreach ($category_products as $product): ?>
                        <div class="col-md-4 col-lg-3 mb-4 product-item" data-category="<?php echo $product['category']; ?>">
                            <div class="card product-card h-100 border-0 shadow-lg" style="transition: transform 0.3s ease;">
                                <!-- صورة المنتج المرنة -->
                                <div class="position-relative product-image-container">
                                    <img src="uploads/products_images/<?php echo $product['image']; ?>" class="card-img-top product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">متوفر</span>
                                    </div>
                                </div>
                                <div class="card-body p-3">
                                    <h5 class="card-title fw-bold text-primary mb-2"><?php echo htmlspecialchars($product['name']); ?></h5>
                                    <p class="card-text text-muted mb-3"><?php echo substr($product['description'], 0, 80); ?>...</p>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="h5 text-success mb-0 fw-bold"><?php echo number_format($product['price']); ?> د.ع</span>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <a href="product-details.php?id=<?php echo $product['id']; ?>" class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                        </a>
                                        <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن ' . $product['name']); ?>" class="btn btn-success" target="_blank">
                                            <i class="fab fa-whatsapp me-1"></i> استفسار عبر واتساب
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</section>

<!-- سكريبت التصفية -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-buttons button');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            // إظهار/إخفاء أقسام المنتجات
            document.querySelectorAll('.product-category').forEach(category => {
                if (filter === 'all') {
                    category.style.display = 'block';
                } else {
                    if (category.getAttribute('data-category') === filter) {
                        category.style.display = 'block';
                    } else {
                        category.style.display = 'none';
                    }
                }
            });
        });
    });
});
</script>

<!-- تحسين مظهر أزرار التصفية والمنتجات -->
<style>
.filter-buttons {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 10px;
}
.filter-buttons button {
    min-width: 120px;
}
.section-title {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: var(--primary-color);
}
.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

/* تحسين عرض المنتجات */
.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.product-card img:hover {
    transform: scale(1.02);
}

/* تحسين عرض صور المنتجات */
.product-image-container {
    background: #f8f9fa;
    border-radius: 0.375rem 0.375rem 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    transition: transform 0.3s ease;
    padding: 15px;
    background: transparent;
}

/* تحسين الأزرار */
.btn {
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

/* تحسينات الموبايل */
@media (max-width: 768px) {
    .product-image-container {
        min-height: 180px;
    }

    .product-image {
        padding: 10px;
    }

    .filter-buttons {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .filter-buttons button {
        min-width: 100px;
        font-size: 0.9rem;
    }
}

/* تحسين إضافي للصور */
.product-image {
    /* إزالة أي قيود على الارتفاع */
    max-height: none !important;
    /* السماح للصورة بأن تأخذ حجمها الطبيعي */
    height: auto !important;
    /* ضمان عدم تشويه الصورة */
    object-fit: contain !important;
}
</style>

<?php require_once 'includes/footer.php'; ?>
