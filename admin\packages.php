<?php
$page_title = 'إدارة البكجات';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// معالجة حذف البكج
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $package_id = $_GET['delete'];

    // الحصول على معلومات البكج للحصول على اسم الصورة
    $stmt = $pdo->prepare("SELECT image FROM packages WHERE id = ?");
    $stmt->execute([$package_id]);
    $package = $stmt->fetch(PDO::FETCH_ASSOC);

    // حذف البكج من قاعدة البيانات
    $delete_stmt = $pdo->prepare("DELETE FROM packages WHERE id = ?");
    $delete_result = $delete_stmt->execute([$package_id]);

    if ($delete_result && $package) {
        // حذف صورة البكج من المجلد
        $image_path = "../uploads/products/" . $package['image'];
        if (file_exists($image_path)) {
            unlink($image_path);
        }

        $success_message = "تم حذف البكج بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البكج";
    }
}

// استعلام للحصول على جميع البكجات
$packages_stmt = $pdo->query("SELECT * FROM packages ORDER BY id DESC");
$packages = $packages_stmt->fetchAll(PDO::FETCH_ASSOC);

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة البكجات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="add_package.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> إضافة بكج جديد
                        </a>
                    </div>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Packages table -->
                <div class="card admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة البكجات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الصورة</th>
                                        <th>العنوان</th>
                                        <th>السعر</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($packages)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">لا توجد بكجات</td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($packages as $package): ?>
                                        <tr>
                                            <td><?php echo $package['id']; ?></td>
                                            <td>
                                                <img src="../uploads/products/<?php echo $package['image']; ?>" alt="<?php echo $package['title']; ?>" width="50" height="50" class="rounded">
                                            </td>
                                            <td><?php echo $package['title']; ?></td>
                                            <td><?php echo number_format($package['price']); ?> د.ع</td>
                                            <td><?php echo date('Y-m-d', strtotime($package['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="edit_package_simple.php?id=<?php echo $package['id']; ?>" class="btn btn-sm btn-success" title="تعديل مبسط">
                                                        <i class="fas fa-edit"></i> سريع
                                                    </a>
                                                    <a href="edit_package_new.php?id=<?php echo $package['id']; ?>" class="btn btn-sm btn-primary" title="تعديل مع صور">
                                                        <i class="fas fa-image"></i> مع صور
                                                    </a>
                                                    <a href="edit_package.php?id=<?php echo $package['id']; ?>" class="btn btn-sm btn-info" title="تعديل متقدم">
                                                        <i class="fas fa-cogs"></i> متقدم
                                                    </a>
                                                </div>
                                                <a href="packages.php?delete=<?php echo $package['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا البكج؟')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
