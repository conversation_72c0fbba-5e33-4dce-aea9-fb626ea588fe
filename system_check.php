<?php
/**
 * ملف فحص النظام - للتأكد من جاهزية الموقع للعمل
 * قم بحذف هذا الملف بعد التأكد من عمل الموقع بشكل صحيح
 */

// بدء الجلسة
session_start();

// إعدادات العرض
ini_set('display_errors', 1);
error_reporting(E_ALL);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - شركة بريق ضوء الشمس</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .check-item { margin-bottom: 1rem; padding: 1rem; border-radius: 0.5rem; }
        .check-pass { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .check-fail { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .check-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .icon-pass { color: #28a745; }
        .icon-fail { color: #dc3545; }
        .icon-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-cogs me-2"></i>فحص النظام - شركة بريق ضوء الشمس</h2>
                        <p class="mb-0">التحقق من جاهزية الموقع للعمل</p>
                    </div>
                    <div class="card-body">

<?php
// فحص إصدار PHP
echo "<h4><i class='fas fa-server me-2'></i>فحص الخادم</h4>";

$php_version = phpversion();
$min_php_version = '7.4.0';
if (version_compare($php_version, $min_php_version, '>=')) {
    echo "<div class='check-item check-pass'>";
    echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
    echo "<strong>إصدار PHP:</strong> $php_version ✅";
    echo "</div>";
} else {
    echo "<div class='check-item check-fail'>";
    echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
    echo "<strong>إصدار PHP:</strong> $php_version ❌ (مطلوب $min_php_version أو أحدث)";
    echo "</div>";
}

// فحص الإضافات المطلوبة
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'mbstring', 'json'];
echo "<h5 class='mt-4'>الإضافات المطلوبة:</h5>";

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='check-item check-pass'>";
        echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
        echo "<strong>$ext:</strong> مثبت ✅";
        echo "</div>";
    } else {
        echo "<div class='check-item check-fail'>";
        echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
        echo "<strong>$ext:</strong> غير مثبت ❌";
        echo "</div>";
    }
}

// فحص قاعدة البيانات
echo "<h4 class='mt-4'><i class='fas fa-database me-2'></i>فحص قاعدة البيانات</h4>";

try {
    require_once 'includes/db_connect.php';
    echo "<div class='check-item check-pass'>";
    echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
    echo "<strong>اتصال قاعدة البيانات:</strong> نجح ✅";
    echo "</div>";
    
    // فحص الجداول المطلوبة
    $required_tables = ['admins', 'products', 'packages', 'company_info', 'media'];
    echo "<h5>الجداول المطلوبة:</h5>";
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<div class='check-item check-pass'>";
            echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
            echo "<strong>جدول $table:</strong> موجود ✅";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='check-item check-fail'>";
            echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
            echo "<strong>جدول $table:</strong> غير موجود ❌";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='check-item check-fail'>";
    echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
    echo "<strong>اتصال قاعدة البيانات:</strong> فشل ❌<br>";
    echo "<small>الخطأ: " . $e->getMessage() . "</small>";
    echo "</div>";
}

// فحص المجلدات والصلاحيات
echo "<h4 class='mt-4'><i class='fas fa-folder me-2'></i>فحص المجلدات والصلاحيات</h4>";

$upload_dirs = [
    'uploads/',
    'uploads/products_images/',
    'uploads/packages_images/',
    'uploads/media/',
    'uploads/sliders/',
    'uploads/team/',
    'uploads/certificates/',
    'uploads/logo/'
];

foreach ($upload_dirs as $dir) {
    if (file_exists($dir)) {
        if (is_writable($dir)) {
            echo "<div class='check-item check-pass'>";
            echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
            echo "<strong>$dir:</strong> موجود وقابل للكتابة ✅";
            echo "</div>";
        } else {
            echo "<div class='check-item check-warning'>";
            echo "<i class='fas fa-exclamation-triangle icon-warning me-2'></i>";
            echo "<strong>$dir:</strong> موجود لكن غير قابل للكتابة ⚠️";
            echo "</div>";
        }
    } else {
        echo "<div class='check-item check-fail'>";
        echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
        echo "<strong>$dir:</strong> غير موجود ❌";
        echo "</div>";
    }
}

// فحص الملفات المهمة
echo "<h4 class='mt-4'><i class='fas fa-file me-2'></i>فحص الملفات المهمة</h4>";

$important_files = [
    '.htaccess' => 'ملف الأمان والتحسينات',
    'includes/db_connect.php' => 'ملف اتصال قاعدة البيانات',
    'includes/functions.php' => 'ملف الدوال المساعدة',
    'admin/index.php' => 'صفحة تسجيل دخول الإدارة',
    'database.sql' => 'ملف قاعدة البيانات'
];

foreach ($important_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='check-item check-pass'>";
        echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
        echo "<strong>$file:</strong> $description ✅";
        echo "</div>";
    } else {
        echo "<div class='check-item check-fail'>";
        echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
        echo "<strong>$file:</strong> $description ❌";
        echo "</div>";
    }
}

// فحص إعدادات PHP
echo "<h4 class='mt-4'><i class='fas fa-cog me-2'></i>إعدادات PHP</h4>";

$php_settings = [
    'file_uploads' => 'رفع الملفات',
    'session.auto_start' => 'بدء الجلسات التلقائي (يجب أن يكون Off)',
];

foreach ($php_settings as $setting => $description) {
    $value = ini_get($setting);
    if ($setting === 'file_uploads') {
        if ($value) {
            echo "<div class='check-item check-pass'>";
            echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
            echo "<strong>$description:</strong> مفعل ✅";
            echo "</div>";
        } else {
            echo "<div class='check-item check-fail'>";
            echo "<i class='fas fa-times-circle icon-fail me-2'></i>";
            echo "<strong>$description:</strong> معطل ❌";
            echo "</div>";
        }
    } elseif ($setting === 'session.auto_start') {
        if (!$value) {
            echo "<div class='check-item check-pass'>";
            echo "<i class='fas fa-check-circle icon-pass me-2'></i>";
            echo "<strong>$description:</strong> معطل ✅";
            echo "</div>";
        } else {
            echo "<div class='check-item check-warning'>";
            echo "<i class='fas fa-exclamation-triangle icon-warning me-2'></i>";
            echo "<strong>$description:</strong> مفعل ⚠️";
            echo "</div>";
        }
    }
}

// عرض معلومات إضافية
echo "<h4 class='mt-4'><i class='fas fa-info-circle me-2'></i>معلومات إضافية</h4>";

echo "<div class='check-item check-pass'>";
echo "<strong>حد رفع الملفات:</strong> " . ini_get('upload_max_filesize') . "<br>";
echo "<strong>حد POST:</strong> " . ini_get('post_max_size') . "<br>";
echo "<strong>حد الذاكرة:</strong> " . ini_get('memory_limit') . "<br>";
echo "<strong>وقت التنفيذ الأقصى:</strong> " . ini_get('max_execution_time') . " ثانية";
echo "</div>";

?>

                    </div>
                    <div class="card-footer text-center">
                        <div class="row">
                            <div class="col-md-4">
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="admin/" class="btn btn-success">
                                    <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                                </a>
                            </div>
                            <div class="col-md-4">
                                <button onclick="location.reload()" class="btn btn-info">
                                    <i class="fas fa-sync me-2"></i>إعادة الفحص
                                </button>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                ⚠️ احذف هذا الملف (system_check.php) بعد التأكد من عمل الموقع بشكل صحيح
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
