-- إن<PERSON><PERSON>ء جدول إنجازات الفريق وخبراته
CREATE TABLE IF NOT EXISTS `team_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('achievement','expertise') NOT NULL DEFAULT 'achievement',
  `title` varchar(255) NOT NULL,
  `value` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `percentage` int(11) DEFAULT NULL,
  `color` varchar(20) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إد<PERSON><PERSON><PERSON> البيانات الافتراضية للإنجازات
INSERT INTO `team_achievements` (`type`, `title`, `value`, `description`, `icon`, `percentage`, `color`, `sort_order`, `status`) VALUES
('achievement', 'مشروع منجز', '+500', 'قمنا بتنفيذ أكثر من 500 مشروع طاقة شمسية بنجاح في مختلف المحافظات', 'fas fa-project-diagram', NULL, 'primary', 1, 1),
('achievement', 'عميل راضٍ', '+1000', 'أكثر من 1000 عميل راضٍ عن خدماتنا ويوصون بنا لأصدقائهم وعائلاتهم', 'fas fa-users', NULL, 'success', 2, 1),
('achievement', 'سنوات خبرة', '+15', 'نمتلك خبرة تزيد عن 15 عاماً في مجال تصميم وتركيب أنظمة الطاقة الشمسية', 'fas fa-award', NULL, 'warning', 3, 1),
('expertise', 'تصميم الأنظمة', NULL, 'خبرة عالية في تصميم أنظمة الطاقة الشمسية المتكاملة', NULL, 95, 'primary', 1, 1),
('expertise', 'تركيب الأنظمة', NULL, 'مهارة متقدمة في تركيب وتشغيل أنظمة الطاقة الشمسية', NULL, 90, 'success', 2, 1),
('expertise', 'خدمة العملاء', NULL, 'التزام عالي بتقديم أفضل خدمة عملاء ودعم فني', NULL, 98, 'info', 3, 1);

-- إنشاء جدول إعدادات قسم الفريق
CREATE TABLE IF NOT EXISTS `team_section_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_name` varchar(100) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `subtitle` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `main_text` text DEFAULT NULL,
  `secondary_text` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `section_name` (`section_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات قسم خبرة وكفاءة
INSERT INTO `team_section_settings` (`section_name`, `title`, `subtitle`, `description`, `main_text`, `secondary_text`, `image`, `status`) VALUES
('achievements', 'خبرة وكفاءة', 'إنجازاتنا', 'نفخر بإنجازاتنا وخبراتنا المتراكمة في مجال الطاقة الشمسية', 'فريق متكامل من الخبراء', 'يتكون فريقنا من مهندسين متخصصين في تصميم أنظمة الطاقة الشمسية وفنيين محترفين في تركيب وصيانة هذه الأنظمة.', 'team.jpg', 1),
('expertise_description', 'وصف الخبرات', NULL, 'نحرص على تدريب فريقنا باستمرار على أحدث التقنيات والمعايير العالمية لضمان تقديم أفضل الخدمات لعملائنا. يمتلك فريقنا خبرة تزيد عن 10 سنوات في مجال الطاقة الشمسية.', NULL, NULL, NULL, 1);
