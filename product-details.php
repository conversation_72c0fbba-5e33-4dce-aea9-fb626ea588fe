<?php
require_once 'includes/header.php';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    // إعادة التوجيه إلى الصفحة الرئيسية إذا لم يتم تحديد معرف المنتج
    header('Location: index.php');
    exit;
}

$product_id = (int)$_GET['id'];

// استعلام للحصول على تفاصيل المنتج
$product_stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
$product_stmt->execute([$product_id]);
$product = $product_stmt->fetch(PDO::FETCH_ASSOC);

// التحقق من وجود المنتج
if (!$product) {
    // إعادة التوجيه إلى الصفحة الرئيسية إذا لم يتم العثور على المنتج
    header('Location: index.php');
    exit;
}

// الحصول على الصور الإضافية للمنتج
$images_stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ?");
$images_stmt->execute([$product_id]);
$product_images = $images_stmt->fetchAll(PDO::FETCH_ASSOC);

// تحديد صفحة العودة بناءً على فئة المنتج
$back_page = 'all_products.php'; // صفحة افتراضية
$category_name = 'المنتجات'; // اسم افتراضي

switch ($product['category']) {
    case 'panel':
        $back_page = 'panels.php';
        $category_name = 'الألواح الشمسية';
        break;
    case 'inverter':
        $back_page = 'inverters.php';
        $category_name = 'الإنفرترات';
        break;
    case 'battery':
        $back_page = 'batteries.php';
        $category_name = 'البطاريات';
        break;
    case 'accessory':
        $back_page = 'accessories.php';
        $category_name = 'الإكسسوارات';
        break;
    case 'accessories':
        $back_page = 'accessories.php';
        $category_name = 'الإكسسوارات';
        break;
    default:
        // للفئات غير المعرفة
        $back_page = 'all_products.php';
        $category_name = ucfirst($product['category']) ?: 'المنتجات';
        break;
}
?>

<!-- عنوان الصفحة -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="<?php echo $back_page; ?>"><?php echo $category_name; ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo $product['name']; ?></li>
            </ol>
        </nav>
    </div>
</div>

<!-- تفاصيل المنتج -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- صور المنتج -->
            <div class="col-md-6 mb-4">
                <div class="product-images">
                    <?php if (!empty($product_images)): ?>
                    <!-- عرض شرائح الصور إذا كانت هناك صور إضافية -->
                    <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <img src="uploads/products/<?php echo $product['image']; ?>" class="d-block w-100 product-detail-img" alt="<?php echo $product['name']; ?>">
                            </div>
                            <?php foreach ($product_images as $image): ?>
                            <div class="carousel-item">
                                <img src="uploads/products/<?php echo $image['image_url']; ?>" class="d-block w-100 product-detail-img" alt="<?php echo $product['name']; ?>">
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">السابق</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">التالي</span>
                        </button>
                        <div class="carousel-indicators position-static mt-2">
                            <button type="button" data-bs-target="#productCarousel" data-bs-slide-to="0" class="active thumbnail-indicator" aria-current="true" aria-label="Slide 1">
                                <img src="uploads/products/<?php echo $product['image']; ?>" class="d-block w-100 thumbnail" alt="<?php echo $product['name']; ?>">
                            </button>
                            <?php $index = 1; foreach ($product_images as $image): ?>
                            <button type="button" data-bs-target="#productCarousel" data-bs-slide-to="<?php echo $index; ?>" class="thumbnail-indicator" aria-label="Slide <?php echo $index + 1; ?>">
                                <img src="uploads/products/<?php echo $image['image_url']; ?>" class="d-block w-100 thumbnail" alt="<?php echo $product['name']; ?>">
                            </button>
                            <?php $index++; endforeach; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- عرض صورة واحدة فقط إذا لم تكن هناك صور إضافية -->
                    <img src="uploads/products/<?php echo $product['image']; ?>" class="img-fluid product-detail-img" alt="<?php echo $product['name']; ?>">
                    <?php endif; ?>
                </div>
            </div>

            <!-- معلومات المنتج -->
            <div class="col-md-6">
                <div class="product-info-container">
                    <h1 class="mb-3 fw-bold text-primary"><?php echo htmlspecialchars($product['name']); ?></h1>

                    <div class="mb-3">
                        <span class="badge bg-primary me-2">
                            <i class="fas fa-tag me-1"></i> <?php echo $category_name; ?>
                        </span>
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i> متوفر
                        </span>
                    </div>

                    <div class="price-container mb-4 p-3 bg-light rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-money-bill-wave text-success me-2 fs-4"></i>
                            <span class="price-large mb-0"><?php echo number_format($product['price']); ?> د.ع</span>
                        </div>
                        <small class="text-muted">السعر شامل الضريبة</small>
                    </div>

                    <div class="product-description mb-4">
                        <h5 class="fw-bold text-secondary mb-3">
                            <i class="fas fa-info-circle me-2"></i> وصف المنتج
                        </h5>
                        <div class="description-content">
                            <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="product-features mb-4">
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="feature-item p-2 bg-light rounded text-center">
                                    <i class="fas fa-shipping-fast text-primary mb-1"></i>
                                    <small class="d-block">توصيل سريع</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item p-2 bg-light rounded text-center">
                                    <i class="fas fa-shield-alt text-success mb-1"></i>
                                    <small class="d-block">ضمان الجودة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item p-2 bg-light rounded text-center">
                                    <i class="fas fa-tools text-warning mb-1"></i>
                                    <small class="d-block">تركيب مجاني</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-item p-2 bg-light rounded text-center">
                                    <i class="fas fa-headset text-info mb-1"></i>
                                    <small class="d-block">دعم فني</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="<?php echo create_whatsapp_link($company_info['phone'], 'مرحباً، أرغب في الاستفسار عن ' . $product['name'] . ' بسعر ' . number_format($product['price']) . ' د.ع'); ?>" class="btn btn-success btn-lg" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i> استفسار عبر واتساب
                        </a>
                        <a href="<?php echo $back_page; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i> العودة إلى <?php echo $category_name; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- إضافة بعض CSS لتحسين مظهر صفحة التفاصيل -->
<style>
/* تحسين عرض الصور الرئيسية */
.product-detail-img {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
}

.product-detail-img:hover {
    transform: scale(1.02);
}

/* تحسين حاوي الصور */
.carousel-inner {
    background: #f8f9fa;
    border-radius: 0.5rem;
    min-height: 300px;
    display: flex;
    align-items: center;
}

.carousel-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

/* تحسين الصور المصغرة */
.thumbnail {
    width: 100%;
    height: 60px;
    object-fit: contain !important;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 5px;
    transition: all 0.3s ease;
}

.thumbnail:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.thumbnail-indicator {
    width: 70px;
    height: 70px;
    border: none;
    padding: 2px;
    opacity: 0.7;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.thumbnail-indicator.active {
    opacity: 1;
    border: 2px solid var(--primary-color);
    transform: scale(1.1);
}

/* تحسين النصوص والأسعار */
.price-large {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.description-content {
    line-height: 1.8;
    text-align: justify;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
}

/* تحسين الشارات */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* تحسين الأزرار */
.btn {
    font-weight: 600;
    transition: all 0.3s ease;
    border-radius: 25px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

.btn-outline-secondary:hover {
    background: linear-gradient(135deg, #6c757d, #495057);
    border-color: #6c757d;
}

/* تحسينات الموبايل */
@media (max-width: 768px) {
    .product-detail-img {
        padding: 15px;
    }

    .carousel-item {
        min-height: 250px;
    }

    .price-large {
        font-size: 1.6rem;
    }

    .description-content {
        padding: 15px;
    }

    .thumbnail-indicator {
        width: 60px;
        height: 60px;
    }
}

/* تحسين breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
    color: var(--primary-color);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* تحسين حاوي معلومات المنتج */
.product-info-container {
    padding: 20px;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسين حاوي السعر */
.price-container {
    border: 2px solid #28a745;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
}

/* تحسين عناصر المميزات */
.feature-item {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.feature-item i {
    font-size: 1.2rem;
}

/* تحسين الشارات */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

/* تحسين عنوان المنتج */
h1.text-primary {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* تحسينات إضافية للموبايل */
@media (max-width: 768px) {
    .product-info-container {
        padding: 15px;
        margin-top: 20px;
    }

    .feature-item {
        margin-bottom: 10px;
    }

    .price-container {
        text-align: center;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
