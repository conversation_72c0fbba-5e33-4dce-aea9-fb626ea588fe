<?php
$page_title = 'تعديل البكج';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

$error = '';
$success = '';

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = $_GET['id'];

// الحصول على بيانات البكج
$stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$stmt->execute([$package_id]);
$package = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$package) {
    header('Location: packages.php');
    exit;
}

// تحويل المكونات من JSON إلى مصفوفة
$components = isset($package['components']) ? json_decode($package['components'], true) : null;

// استخراج بيانات المكونات
$panel_name = isset($components['panel']['name']) ? $components['panel']['name'] : '';
$panel_count = isset($components['panel']['count']) ? $components['panel']['count'] : '';
$panel_power = isset($components['panel']['power']) ? $components['panel']['power'] : '';
$panel_image = isset($components['panel']['image']) ? $components['panel']['image'] : '';

$battery_name = isset($components['battery']['name']) ? $components['battery']['name'] : '';
$battery_count = isset($components['battery']['count']) ? $components['battery']['count'] : '';
$battery_power = isset($components['battery']['power']) ? $components['battery']['power'] : '';
$battery_image = isset($components['battery']['image']) ? $components['battery']['image'] : '';

$inverter_name = isset($components['inverter']['name']) ? $components['inverter']['name'] : '';
$inverter_count = isset($components['inverter']['count']) ? $components['inverter']['count'] : '';
$inverter_power = isset($components['inverter']['power']) ? $components['inverter']['power'] : '';
$inverter_image = isset($components['inverter']['image']) ? $components['inverter']['image'] : '';

// معالجة نموذج تعديل البكج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // إضافة تشخيص لمعرفة ما يتم إرساله
    error_log("POST Data received: " . print_r($_POST, true));

    // تشخيص مباشر للحقول الأساسية
    $debug_info = "البيانات المستلمة: ";
    $debug_info .= "title=" . (isset($_POST['title']) ? $_POST['title'] : 'غير موجود') . ", ";
    $debug_info .= "description=" . (isset($_POST['description']) ? substr($_POST['description'], 0, 50) : 'غير موجود') . ", ";
    $debug_info .= "price=" . (isset($_POST['price']) ? $_POST['price'] : 'غير موجود');

    $success = $debug_info;

    // التحقق من البيانات المدخلة
    try {
        // التحقق من وجود البيانات في POST
        if (!isset($_POST['title']) || !isset($_POST['description']) || !isset($_POST['price'])) {
            $error = 'بيانات النموذج غير مكتملة';
            $success = 'خطأ: بيانات النموذج غير مكتملة';
        } else {
            $title = clean_input($_POST['title']);
            $description = clean_input($_POST['description']);
            $price = clean_input($_POST['price']);

            $success = 'تم تنظيف البيانات بنجاح - العنوان: ' . $title . ', السعر: ' . $price;

            // تشخيص البيانات المستلمة
            error_log("Cleaned Data: Title=$title, Description=$description, Price=$price");
        }
    } catch (Exception $e) {
        $error = 'خطأ في تنظيف البيانات: ' . $e->getMessage();
        $success = 'خطأ في تنظيف البيانات: ' . $e->getMessage();
    }

    // بيانات الألواح الشمسية
    if (!isset($error)) {
        try {
            // التحقق من وجود بيانات المكونات
            $required_fields = ['panel_name', 'panel_count', 'panel_power', 'battery_name', 'battery_count', 'battery_power', 'inverter_name', 'inverter_count', 'inverter_power'];
            $missing_fields = [];

            foreach ($required_fields as $field) {
                if (!isset($_POST[$field])) {
                    $missing_fields[] = $field;
                }
            }

            if (!empty($missing_fields)) {
                $error = 'حقول مكونات مفقودة: ' . implode(', ', $missing_fields);
                $success = 'خطأ: حقول مكونات مفقودة: ' . implode(', ', $missing_fields);
            } else {
                $panel_name = clean_input($_POST['panel_name']);
                $panel_count = clean_input($_POST['panel_count']);
                $panel_power = clean_input($_POST['panel_power']);

                // بيانات البطاريات
                $battery_name = clean_input($_POST['battery_name']);
                $battery_count = clean_input($_POST['battery_count']);
                $battery_power = clean_input($_POST['battery_power']);

                // بيانات الإنفرترات
                $inverter_name = clean_input($_POST['inverter_name']);
                $inverter_count = clean_input($_POST['inverter_count']);
                $inverter_power = clean_input($_POST['inverter_power']);

                $success = 'تم تنظيف بيانات المكونات بنجاح - اللوح: ' . $panel_name . ', البطارية: ' . $battery_name . ', الإنفرتر: ' . $inverter_name;
            }
        } catch (Exception $e) {
            $error = 'خطأ في تنظيف بيانات المكونات: ' . $e->getMessage();
            $success = 'خطأ في تنظيف بيانات المكونات: ' . $e->getMessage();
        }
    } else {
        $success = 'تم تخطي تنظيف بيانات المكونات بسبب خطأ سابق - الخطأ: ' . (isset($error) ? $error : 'غير محدد');
    }

    // تشخيص حالة المتغير error
    $success .= ' | حالة المتغير error: ' . (isset($error) ? 'موجود - ' . $error : 'غير موجود');

    if (!isset($error)) {
        // التحقق من الحقول المطلوبة
        $success = 'وصلنا إلى نقطة التحقق من الحقول - جاري التحقق من الحقول المطلوبة...';

        if (empty($title) || empty($description) || empty($price)) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
            $success = 'خطأ: حقول مطلوبة فارغة - العنوان: "' . $title . '", الوصف: "' . substr($description, 0, 50) . '", السعر: "' . $price . '"';
        } elseif (!is_numeric($price) || $price <= 0) {
            $error = 'يرجى إدخال سعر صحيح';
            $success = 'خطأ: سعر غير صحيح - القيمة المدخلة: "' . $price . '"';
        } else {
            $success = 'تم التحقق من البيانات بنجاح - جاري رفع الصور...';
            $upload_dir = '../uploads/products/';

        // التأكد من وجود المجلد
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // رفع الصورة الرئيسية الجديدة
        $main_image_updated = false;
        $main_image_filename = $package['image'];

        if ($_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $success = 'جاري رفع الصورة الرئيسية...';
            $upload_result = upload_file($_FILES['image'], $upload_dir);

            if ($upload_result['success']) {
                // حذف الصورة القديمة
                $old_image_path = $upload_dir . $package['image'];
                if (file_exists($old_image_path)) {
                    unlink($old_image_path);
                }

                $main_image_filename = $upload_result['filename'];
                $main_image_updated = true;
                $success = 'تم رفع الصورة الرئيسية بنجاح';
            } else {
                $error = 'خطأ في رفع الصورة الرئيسية: ' . $upload_result['message'];
                $success = 'خطأ في رفع الصورة الرئيسية: ' . $upload_result['message'];
            }
        } else {
            $success = 'لم يتم اختيار صورة رئيسية جديدة - سيتم الاحتفاظ بالصورة الحالية';
        }

        // رفع صورة اللوح الشمسي
        if (!isset($error) && $_FILES['panel_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $success = 'جاري رفع صورة اللوح الشمسي...';
            $panel_upload_result = upload_file($_FILES['panel_image'], $upload_dir);
            if ($panel_upload_result['success']) {
                // حذف الصورة القديمة إذا وجدت
                if (!empty($panel_image)) {
                    $old_panel_image_path = $upload_dir . $panel_image;
                    if (file_exists($old_panel_image_path)) {
                        unlink($old_panel_image_path);
                    }
                }

                $panel_image = $panel_upload_result['filename'];
                $success = 'تم رفع صورة اللوح الشمسي بنجاح';
            } else {
                $error = 'خطأ في رفع صورة اللوح الشمسي: ' . $panel_upload_result['message'];
                $success = 'خطأ في رفع صورة اللوح الشمسي: ' . $panel_upload_result['message'];
            }
        } else if (!isset($error)) {
            $success = 'لم يتم اختيار صورة جديدة للوح الشمسي';
        }

        // رفع صورة البطارية
        if (!isset($error) && $_FILES['battery_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $success = 'جاري رفع صورة البطارية...';
            $battery_upload_result = upload_file($_FILES['battery_image'], $upload_dir);
            if ($battery_upload_result['success']) {
                // حذف الصورة القديمة إذا وجدت
                if (!empty($battery_image)) {
                    $old_battery_image_path = $upload_dir . $battery_image;
                    if (file_exists($old_battery_image_path)) {
                        unlink($old_battery_image_path);
                    }
                }

                $battery_image = $battery_upload_result['filename'];
                $success = 'تم رفع صورة البطارية بنجاح';
            } else {
                $error = 'خطأ في رفع صورة البطارية: ' . $battery_upload_result['message'];
                $success = 'خطأ في رفع صورة البطارية: ' . $battery_upload_result['message'];
            }
        } else if (!isset($error)) {
            $success = 'لم يتم اختيار صورة جديدة للبطارية';
        }

        // رفع صورة الإنفرتر
        if (!isset($error) && $_FILES['inverter_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $success = 'جاري رفع صورة الإنفرتر...';
            $inverter_upload_result = upload_file($_FILES['inverter_image'], $upload_dir);
            if ($inverter_upload_result['success']) {
                // حذف الصورة القديمة إذا وجدت
                if (!empty($inverter_image)) {
                    $old_inverter_image_path = $upload_dir . $inverter_image;
                    if (file_exists($old_inverter_image_path)) {
                        unlink($old_inverter_image_path);
                    }
                }

                $inverter_image = $inverter_upload_result['filename'];
                $success = 'تم رفع صورة الإنفرتر بنجاح';
            } else {
                $error = 'خطأ في رفع صورة الإنفرتر: ' . $inverter_upload_result['message'];
                $success = 'خطأ في رفع صورة الإنفرتر: ' . $inverter_upload_result['message'];
            }
        } else if (!isset($error)) {
            $success = 'لم يتم اختيار صورة جديدة للإنفرتر - جميع الصور تمت معالجتها بنجاح';
        }

        if (!isset($error)) {
            $success = 'تم رفع الصور بنجاح - جاري تحديث قاعدة البيانات...';
            try {
                // إنشاء مصفوفة لتفاصيل المكونات
                $components = [
                    'panel' => [
                        'name' => $panel_name,
                        'count' => $panel_count,
                        'power' => $panel_power,
                        'image' => $panel_image
                    ],
                    'battery' => [
                        'name' => $battery_name,
                        'count' => $battery_count,
                        'power' => $battery_power,
                        'image' => $battery_image
                    ],
                    'inverter' => [
                        'name' => $inverter_name,
                        'count' => $inverter_count,
                        'power' => $inverter_power,
                        'image' => $inverter_image
                    ]
                ];

                // تحويل مصفوفة المكونات إلى JSON
                $components_json = json_encode($components);

                // إنشاء مصفوفة فارغة للمنتجات
                $empty_product_ids = json_encode([]);

                // تحديث البكج
                $success = 'جاري تنفيذ استعلام التحديث...';
                $stmt = $pdo->prepare("UPDATE packages SET title = ?, description = ?, image = ?, price = ?, components = ?, product_ids = ? WHERE id = ?");
                $result = $stmt->execute([$title, $description, $main_image_filename, $price, $components_json, $empty_product_ids, $package_id]);

                // إضافة تشخيص للتحقق من البيانات المرسلة
                error_log("Package Update Debug: ID=$package_id, Title=$title, Price=$price, Components=$components_json");

                // تشخيص SQL query
                error_log("SQL Query: UPDATE packages SET title = '$title', description = '$description', image = '$main_image_filename', price = '$price', components = '$components_json', product_ids = '$empty_product_ids' WHERE id = '$package_id'");

                $success = 'تم تنفيذ الاستعلام - جاري التحقق من النتيجة...';

            if ($result) {
                // التحقق من عدد الصفوف المتأثرة
                $affected_rows = $stmt->rowCount();
                error_log("Affected rows: $affected_rows");

                if ($affected_rows > 0) {
                    $success = 'تم تحديث البكج بنجاح! عدد الصفوف المتأثرة: ' . $affected_rows;
                } else {
                    $success = 'لم يتم تحديث أي صفوف. عدد الصفوف المتأثرة: ' . $affected_rows;
                    // التحقق من البيانات الحالية مقابل البيانات الجديدة
                    $current_stmt = $pdo->prepare("SELECT title, description, price, components FROM packages WHERE id = ?");
                    $current_stmt->execute([$package_id]);
                    $current_data = $current_stmt->fetch(PDO::FETCH_ASSOC);

                    error_log("Current data: " . print_r($current_data, true));

                    $current_components = json_decode($current_data['components'], true);
                    $new_components = json_decode($components_json, true);

                    // مقارنة مفصلة للبيانات
                    $title_same = ($current_data['title'] == $title);
                    $desc_same = ($current_data['description'] == $description);
                    $price_same = ($current_data['price'] == $price);
                    $comp_same = (json_encode($current_components) == json_encode($new_components));

                    $success .= " | مقارنة البيانات: العنوان=" . ($title_same ? 'متطابق' : 'مختلف') .
                               ", الوصف=" . ($desc_same ? 'متطابق' : 'مختلف') .
                               ", السعر=" . ($price_same ? 'متطابق' : 'مختلف') .
                               ", المكونات=" . ($comp_same ? 'متطابق' : 'مختلف');

                    if ($title_same && $desc_same && $price_same && $comp_same) {
                        $error = 'لم يتم إجراء أي تغييرات على البيانات.';
                    } else {
                        $error = 'حدث خطأ غير متوقع أثناء التحديث. البيانات الحالية مختلفة عن البيانات الجديدة.';
                        error_log("Data comparison failed - update should have happened");
                    }
                }

                // إعادة تحميل بيانات البكج
                $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
                $stmt->execute([$package_id]);
                $package = $stmt->fetch(PDO::FETCH_ASSOC);

                // تحويل المكونات من JSON إلى مصفوفة
                $components = isset($package['components']) ? json_decode($package['components'], true) : null;

                // استخراج بيانات المكونات
                $panel_name = isset($components['panel']['name']) ? $components['panel']['name'] : '';
                $panel_count = isset($components['panel']['count']) ? $components['panel']['count'] : '';
                $panel_power = isset($components['panel']['power']) ? $components['panel']['power'] : '';
                $panel_image = isset($components['panel']['image']) ? $components['panel']['image'] : '';

                $battery_name = isset($components['battery']['name']) ? $components['battery']['name'] : '';
                $battery_count = isset($components['battery']['count']) ? $components['battery']['count'] : '';
                $battery_power = isset($components['battery']['power']) ? $components['battery']['power'] : '';
                $battery_image = isset($components['battery']['image']) ? $components['battery']['image'] : '';

                $inverter_name = isset($components['inverter']['name']) ? $components['inverter']['name'] : '';
                $inverter_count = isset($components['inverter']['count']) ? $components['inverter']['count'] : '';
                $inverter_power = isset($components['inverter']['power']) ? $components['inverter']['power'] : '';
                $inverter_image = isset($components['inverter']['image']) ? $components['inverter']['image'] : '';
            } else {
                $error = 'حدث خطأ أثناء تحديث البكج - فشل في تنفيذ الاستعلام';
                $success = 'خطأ: فشل تنفيذ الاستعلام';
                error_log("SQL execution failed");
            }
            } catch (Exception $e) {
                $error = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
                $success = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
                error_log("Database exception: " . $e->getMessage());
            }
        } else {
            $success = 'خطأ: فشل التحقق من البيانات أو رفع الصور';
            error_log("Form validation failed or file upload error occurred");
        }
        }
    }
} else {
    error_log("No POST request received");
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تعديل البكج</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="packages.php" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة إلى قائمة البكجات
                        </a>
                    </div>
                </div>

                <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Edit package form -->
                <div class="card admin-card">
                    <div class="card-header">
                        <h5 class="mb-0">تعديل البكج: <?php echo $package['title']; ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">عنوان البكج <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?php echo $package['title']; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $package['price']; ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف البكج <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo $package['description']; ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="image" class="form-label">صورة البكج</label>
                                <input type="file" class="form-control image-upload" id="image" name="image" accept="image/*" data-preview="image-preview">
                                <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                                <div class="mt-2">
                                    <img id="image-preview" src="../uploads/products/<?php echo $package['image']; ?>" alt="<?php echo $package['title']; ?>" class="img-thumbnail" style="max-height: 200px;">
                                </div>
                            </div>

                            <h5 class="mt-4 mb-3">تفاصيل مكونات البكج</h5>

                            <!-- تفاصيل الألواح الشمسية -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-solar-panel me-2"></i> تفاصيل الألواح الشمسية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_name" class="form-label">اسم اللوح</label>
                                            <input type="text" class="form-control" id="panel_name" name="panel_name" value="<?php echo $panel_name; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_count" class="form-label">عدد الألواح</label>
                                            <input type="number" class="form-control" id="panel_count" name="panel_count" min="0" value="<?php echo $panel_count; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="panel_power" class="form-label">قدرة اللوح (واط)</label>
                                            <input type="text" class="form-control" id="panel_power" name="panel_power" value="<?php echo $panel_power; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="panel_image" class="form-label">صورة اللوح</label>
                                        <input type="file" class="form-control image-upload" id="panel_image" name="panel_image" accept="image/*" data-preview="panel-image-preview">
                                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                                        <?php if (!empty($panel_image)): ?>
                                        <div class="mt-2">
                                            <img id="panel-image-preview" src="../uploads/products/<?php echo $panel_image; ?>" alt="صورة اللوح" class="img-thumbnail" style="max-height: 150px;">
                                        </div>
                                        <?php else: ?>
                                        <div class="mt-2">
                                            <img id="panel-image-preview" src="#" alt="معاينة صورة اللوح" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل البطاريات -->
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-car-battery me-2"></i> تفاصيل بطاريات الليثيوم</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_name" class="form-label">اسم البطارية</label>
                                            <input type="text" class="form-control" id="battery_name" name="battery_name" value="<?php echo $battery_name; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_count" class="form-label">عدد البطاريات</label>
                                            <input type="number" class="form-control" id="battery_count" name="battery_count" min="0" value="<?php echo $battery_count; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="battery_power" class="form-label">قدرة البطارية (أمبير)</label>
                                            <input type="text" class="form-control" id="battery_power" name="battery_power" value="<?php echo $battery_power; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="battery_image" class="form-label">صورة البطارية</label>
                                        <input type="file" class="form-control image-upload" id="battery_image" name="battery_image" accept="image/*" data-preview="battery-image-preview">
                                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                                        <?php if (!empty($battery_image)): ?>
                                        <div class="mt-2">
                                            <img id="battery-image-preview" src="../uploads/products/<?php echo $battery_image; ?>" alt="صورة البطارية" class="img-thumbnail" style="max-height: 150px;">
                                        </div>
                                        <?php else: ?>
                                        <div class="mt-2">
                                            <img id="battery-image-preview" src="#" alt="معاينة صورة البطارية" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل الإنفرترات -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-plug me-2"></i> تفاصيل الإنفرترات</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_name" class="form-label">اسم الإنفرتر</label>
                                            <input type="text" class="form-control" id="inverter_name" name="inverter_name" value="<?php echo $inverter_name; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_count" class="form-label">عدد الإنفرترات</label>
                                            <input type="number" class="form-control" id="inverter_count" name="inverter_count" min="0" value="<?php echo $inverter_count; ?>">
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="inverter_power" class="form-label">قدرة الإنفرتر (واط)</label>
                                            <input type="text" class="form-control" id="inverter_power" name="inverter_power" value="<?php echo $inverter_power; ?>">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="inverter_image" class="form-label">صورة الإنفرتر</label>
                                        <input type="file" class="form-control image-upload" id="inverter_image" name="inverter_image" accept="image/*" data-preview="inverter-image-preview">
                                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                                        <?php if (!empty($inverter_image)): ?>
                                        <div class="mt-2">
                                            <img id="inverter-image-preview" src="../uploads/products/<?php echo $inverter_image; ?>" alt="صورة الإنفرتر" class="img-thumbnail" style="max-height: 150px;">
                                        </div>
                                        <?php else: ?>
                                        <div class="mt-2">
                                            <img id="inverter-image-preview" src="#" alt="معاينة صورة الإنفرتر" class="img-thumbnail" style="max-height: 150px; display: none;">
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظة: تم استبدال قسم اختيار المنتجات بقسم تفاصيل مكونات البكج -->
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle me-2"></i> يمكنك إضافة تفاصيل مكونات البكج في الأقسام أعلاه. هذه التفاصيل ستظهر في صفحة تفاصيل البكج.
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="packages.php" class="btn btn-secondary">إلغاء</a>
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                            </div>
                        </form>
                    </div>
                </div>

    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <script>
        // معاينة الصور عند اختيارها
        document.addEventListener('DOMContentLoaded', function() {
            // وظيفة لمعاينة الصور
            function setupImagePreview(inputSelector) {
                const input = document.querySelector(inputSelector);
                if (input) {
                    input.addEventListener('change', function() {
                        const previewId = this.getAttribute('data-preview');
                        const preview = document.getElementById(previewId);

                        if (this.files && this.files[0]) {
                            const reader = new FileReader();

                            reader.onload = function(e) {
                                preview.src = e.target.result;
                                preview.style.display = 'block';
                            };

                            reader.readAsDataURL(this.files[0]);
                        }
                    });
                }
            }

            // إعداد معاينة لجميع حقول الصور
            setupImagePreview('#image');
            setupImagePreview('#panel_image');
            setupImagePreview('#battery_image');
            setupImagePreview('#inverter_image');
        });
    </script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
