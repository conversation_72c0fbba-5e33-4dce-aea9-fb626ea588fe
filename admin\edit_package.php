<?php
$page_title = 'تعديل البكج';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// التحقق من وجود معرف البكج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: packages.php');
    exit;
}

$package_id = (int)$_GET['id'];

// جلب بيانات البكج
$stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
$stmt->execute([$package_id]);
$package = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$package) {
    header('Location: packages.php');
    exit;
}

// استخراج بيانات المكونات من JSON
$components = isset($package['components']) ? json_decode($package['components'], true) : [];

// بيانات الألواح الشمسية
$panel_name = $components['panel']['name'] ?? '';
$panel_count = $components['panel']['count'] ?? '';
$panel_power = $components['panel']['power'] ?? '';
$panel_image = $components['panel']['image'] ?? '';

// بيانات البطاريات
$battery_name = $components['battery']['name'] ?? '';
$battery_count = $components['battery']['count'] ?? '';
$battery_power = $components['battery']['power'] ?? '';
$battery_image = $components['battery']['image'] ?? '';

// بيانات الإنفرترات
$inverter_name = $components['inverter']['name'] ?? '';
$inverter_count = $components['inverter']['count'] ?? '';
$inverter_power = $components['inverter']['power'] ?? '';
$inverter_image = $components['inverter']['image'] ?? '';

$success = '';
$error = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // البيانات الأساسية
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = trim($_POST['price'] ?? '');

    // بيانات المكونات
    $panel_name = trim($_POST['panel_name'] ?? '');
    $panel_count = trim($_POST['panel_count'] ?? '');
    $panel_power = trim($_POST['panel_power'] ?? '');

    $battery_name = trim($_POST['battery_name'] ?? '');
    $battery_count = trim($_POST['battery_count'] ?? '');
    $battery_power = trim($_POST['battery_power'] ?? '');

    $inverter_name = trim($_POST['inverter_name'] ?? '');
    $inverter_count = trim($_POST['inverter_count'] ?? '');
    $inverter_power = trim($_POST['inverter_power'] ?? '');

    // التحقق من البيانات الأساسية
    if (empty($title) || empty($description) || empty($price)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة (العنوان، الوصف، السعر)';
    } elseif (!is_numeric($price) || $price <= 0) {
        $error = 'يرجى إدخال سعر صحيح';
    } else {
        try {
            $upload_dir = '../uploads/packages_images/';

            // التأكد من وجود المجلد
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            // معالجة الصور
            $main_image = $package['image']; // الاحتفاظ بالصورة الحالية

            // رفع الصورة الرئيسية
            if (isset($_FILES['image']) && $_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
                $upload_result = upload_file($_FILES['image'], $upload_dir);
                if ($upload_result['success']) {
                    // حذف الصورة القديمة
                    if (!empty($package['image']) && file_exists($upload_dir . $package['image'])) {
                        unlink($upload_dir . $package['image']);
                    }
                    $main_image = $upload_result['filename'];
                } else {
                    $error = 'خطأ في رفع الصورة الرئيسية: ' . $upload_result['message'];
                }
            }

            // رفع صورة اللوح الشمسي
            if (empty($error) && isset($_FILES['panel_image']) && $_FILES['panel_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                $upload_result = upload_file($_FILES['panel_image'], $upload_dir);
                if ($upload_result['success']) {
                    // حذف الصورة القديمة
                    if (!empty($panel_image) && file_exists($upload_dir . $panel_image)) {
                        unlink($upload_dir . $panel_image);
                    }
                    $panel_image = $upload_result['filename'];
                } else {
                    $error = 'خطأ في رفع صورة اللوح الشمسي: ' . $upload_result['message'];
                }
            }

            // رفع صورة البطارية
            if (empty($error) && isset($_FILES['battery_image']) && $_FILES['battery_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                $upload_result = upload_file($_FILES['battery_image'], $upload_dir);
                if ($upload_result['success']) {
                    // حذف الصورة القديمة
                    if (!empty($battery_image) && file_exists($upload_dir . $battery_image)) {
                        unlink($upload_dir . $battery_image);
                    }
                    $battery_image = $upload_result['filename'];
                } else {
                    $error = 'خطأ في رفع صورة البطارية: ' . $upload_result['message'];
                }
            }

            // رفع صورة الإنفرتر
            if (empty($error) && isset($_FILES['inverter_image']) && $_FILES['inverter_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                $upload_result = upload_file($_FILES['inverter_image'], $upload_dir);
                if ($upload_result['success']) {
                    // حذف الصورة القديمة
                    if (!empty($inverter_image) && file_exists($upload_dir . $inverter_image)) {
                        unlink($upload_dir . $inverter_image);
                    }
                    $inverter_image = $upload_result['filename'];
                } else {
                    $error = 'خطأ في رفع صورة الإنفرتر: ' . $upload_result['message'];
                }
            }

            // تحديث البكج إذا لم يكن هناك خطأ
            if (empty($error)) {
                // إنشاء مصفوفة المكونات
                $components_data = [
                    'panel' => [
                        'name' => $panel_name,
                        'count' => $panel_count,
                        'power' => $panel_power,
                        'image' => $panel_image
                    ],
                    'battery' => [
                        'name' => $battery_name,
                        'count' => $battery_count,
                        'power' => $battery_power,
                        'image' => $battery_image
                    ],
                    'inverter' => [
                        'name' => $inverter_name,
                        'count' => $inverter_count,
                        'power' => $inverter_power,
                        'image' => $inverter_image
                    ]
                ];

                $components_json = json_encode($components_data);

                // تحديث قاعدة البيانات
                $stmt = $pdo->prepare("UPDATE packages SET title = ?, description = ?, price = ?, image = ?, components = ? WHERE id = ?");
                $result = $stmt->execute([$title, $description, $price, $main_image, $components_json, $package_id]);

                if ($result) {
                    $success = 'تم تحديث البكج بنجاح!';

                    // إعادة جلب البيانات المحدثة
                    $stmt = $pdo->prepare("SELECT * FROM packages WHERE id = ?");
                    $stmt->execute([$package_id]);
                    $package = $stmt->fetch(PDO::FETCH_ASSOC);

                    // تحديث متغيرات المكونات
                    $components = json_decode($package['components'], true) ?? [];
                    $panel_name = $components['panel']['name'] ?? '';
                    $panel_count = $components['panel']['count'] ?? '';
                    $panel_power = $components['panel']['power'] ?? '';
                    $panel_image = $components['panel']['image'] ?? '';
                    $battery_name = $components['battery']['name'] ?? '';
                    $battery_count = $components['battery']['count'] ?? '';
                    $battery_power = $components['battery']['power'] ?? '';
                    $battery_image = $components['battery']['image'] ?? '';
                    $inverter_name = $components['inverter']['name'] ?? '';
                    $inverter_count = $components['inverter']['count'] ?? '';
                    $inverter_power = $components['inverter']['power'] ?? '';
                    $inverter_image = $components['inverter']['image'] ?? '';
                } else {
                    $error = 'حدث خطأ أثناء تحديث البكج';
                }
            }

        } catch (Exception $e) {
            $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل البكج</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="packages.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى البكجات
        </a>
    </div>
</div>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">تعديل البكج: <?php echo htmlspecialchars($package['title']); ?></h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <!-- البيانات الأساسية -->
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="title" class="form-label">عنوان البكج <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($package['title']); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $package['price']; ?>" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">وصف البكج <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($package['description']); ?></textarea>
            </div>

            <div class="mb-4">
                <label for="image" class="form-label">صورة البكج الرئيسية</label>
                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                <?php if (!empty($package['image'])): ?>
                <div class="mt-2">
                    <img src="../uploads/packages_images/<?php echo $package['image']; ?>" alt="<?php echo htmlspecialchars($package['title']); ?>" class="img-thumbnail" style="max-height: 200px;">
                </div>
                <?php endif; ?>
            </div>

            <h5 class="mt-4 mb-3">تفاصيل مكونات البكج</h5>

            <!-- تفاصيل الألواح الشمسية -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-solar-panel me-2"></i> تفاصيل الألواح الشمسية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="panel_name" class="form-label">اسم اللوح</label>
                            <input type="text" class="form-control" id="panel_name" name="panel_name" value="<?php echo htmlspecialchars($panel_name); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="panel_count" class="form-label">عدد الألواح</label>
                            <input type="number" class="form-control" id="panel_count" name="panel_count" min="0" value="<?php echo htmlspecialchars($panel_count); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="panel_power" class="form-label">قدرة اللوح (واط)</label>
                            <input type="text" class="form-control" id="panel_power" name="panel_power" value="<?php echo htmlspecialchars($panel_power); ?>">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="panel_image" class="form-label">صورة اللوح</label>
                        <input type="file" class="form-control" id="panel_image" name="panel_image" accept="image/*">
                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                        <?php if (!empty($panel_image)): ?>
                        <div class="mt-2">
                            <img src="../uploads/packages_images/<?php echo $panel_image; ?>" alt="صورة اللوح" class="img-thumbnail" style="max-height: 150px;">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تفاصيل البطاريات -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-car-battery me-2"></i> تفاصيل بطاريات الليثيوم</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="battery_name" class="form-label">اسم البطارية</label>
                            <input type="text" class="form-control" id="battery_name" name="battery_name" value="<?php echo htmlspecialchars($battery_name); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="battery_count" class="form-label">عدد البطاريات</label>
                            <input type="number" class="form-control" id="battery_count" name="battery_count" min="0" value="<?php echo htmlspecialchars($battery_count); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="battery_power" class="form-label">قدرة البطارية (أمبير)</label>
                            <input type="text" class="form-control" id="battery_power" name="battery_power" value="<?php echo htmlspecialchars($battery_power); ?>">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="battery_image" class="form-label">صورة البطارية</label>
                        <input type="file" class="form-control" id="battery_image" name="battery_image" accept="image/*">
                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                        <?php if (!empty($battery_image)): ?>
                        <div class="mt-2">
                            <img src="../uploads/packages_images/<?php echo $battery_image; ?>" alt="صورة البطارية" class="img-thumbnail" style="max-height: 150px;">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الإنفرترات -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-plug me-2"></i> تفاصيل الإنفرترات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="inverter_name" class="form-label">اسم الإنفرتر</label>
                            <input type="text" class="form-control" id="inverter_name" name="inverter_name" value="<?php echo htmlspecialchars($inverter_name); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="inverter_count" class="form-label">عدد الإنفرترات</label>
                            <input type="number" class="form-control" id="inverter_count" name="inverter_count" min="0" value="<?php echo htmlspecialchars($inverter_count); ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="inverter_power" class="form-label">قدرة الإنفرتر (واط)</label>
                            <input type="text" class="form-control" id="inverter_power" name="inverter_power" value="<?php echo htmlspecialchars($inverter_power); ?>">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="inverter_image" class="form-label">صورة الإنفرتر</label>
                        <input type="file" class="form-control" id="inverter_image" name="inverter_image" accept="image/*">
                        <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة</small>
                        <?php if (!empty($inverter_image)): ?>
                        <div class="mt-2">
                            <img src="../uploads/packages_images/<?php echo $inverter_image; ?>" alt="صورة الإنفرتر" class="img-thumbnail" style="max-height: 150px;">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> يمكنك تعديل جميع بيانات البكج من هذه الصفحة. الحقول المطلوبة هي العنوان والوصف والسعر فقط.
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="packages.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>