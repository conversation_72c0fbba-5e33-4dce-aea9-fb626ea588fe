<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// الحصول على إعدادات الموقع
$site_settings = get_site_settings($pdo);

// استعلام للحصول على وسائط المعرض
try {
    $media_stmt = $pdo->query("SELECT * FROM media ORDER BY created_at DESC");
    $media_items = $media_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $media_items = [];
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>



<!-- قسم المعرض الرئيسي -->
<section class="gallery-main-section py-5 mt-5">
    <div class="container">

        <!-- إحصائيات سريعة -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo count(array_filter($media_items, function($item) { return $item['type'] == 'image'; })); ?></h3>
                        <p>صورة</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="stats-content">
                        <h3><?php echo count(array_filter($media_items, function($item) { return $item['type'] == 'video'; })); ?></h3>
                        <p>فيديو</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stats-content">
                        <h3>500+</h3>
                        <p>مشروع منجز</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-content">
                        <h3>1000+</h3>
                        <p>عميل راضٍ</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التصفية -->
        <div class="gallery-filters mb-5">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="filter-buttons text-center">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-th me-2"></i>جميع الأعمال
                        </button>
                        <button class="filter-btn" data-filter="images">
                            <i class="fas fa-images me-2"></i>الصور
                        </button>
                        <button class="filter-btn" data-filter="videos">
                            <i class="fas fa-video me-2"></i>الفيديوهات
                        </button>
                        <button class="filter-btn" data-filter="installation">
                            <i class="fas fa-tools me-2"></i>أعمال التركيب
                        </button>
                        <button class="filter-btn" data-filter="maintenance">
                            <i class="fas fa-wrench me-2"></i>أعمال الصيانة
                        </button>
                        <button class="filter-btn" data-filter="projects">
                            <i class="fas fa-check-circle me-2"></i>المشاريع المنجزة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($media_items)): ?>
        <!-- رسالة عدم وجود محتوى -->
        <div class="row">
            <div class="col-12">
                <div class="no-content-message">
                    <div class="no-content-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <h3>لا توجد أعمال متاحة حالياً</h3>
                    <p>سيتم إضافة أعمالنا ومشاريعنا قريباً. تابعونا للاطلاع على آخر أعمالنا.</p>
                    <a href="contact.php" class="btn btn-primary">
                        <i class="fas fa-phone me-2"></i>تواصل معنا
                    </a>
                </div>
            </div>
        </div>
        <?php else: ?>

        <!-- شبكة المعرض -->
        <div class="gallery-grid" id="galleryGrid">
            <?php
            foreach ($media_items as $media):
                // استخدام الفئة الحقيقية من قاعدة البيانات
                $category = $media['category'];

                // تحديد نوع الملف
                $file_type = $media['type'] == 'video' ? 'videos' : 'images';
            ?>
            <div class="gallery-item" data-category="<?php echo $file_type; ?> <?php echo $category; ?>">
                <div class="gallery-card">
                    <?php if ($media['type'] == 'image'): ?>
                    <!-- عنصر صورة -->
                    <div class="gallery-image">
                        <div class="gallery-image-container">
                            <img src="uploads/media/<?php echo $media['file_url']; ?>" alt="<?php echo htmlspecialchars($media['title']); ?>" loading="lazy" class="gallery-img">
                        </div>
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="gallery-action-btn" onclick="openGalleryLightbox('uploads/media/<?php echo $media['file_url']; ?>', '<?php echo addslashes($media['title']); ?>', 'image')">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="gallery-action-btn" onclick="shareMedia('<?php echo $media['file_url']; ?>', '<?php echo addslashes($media['title']); ?>')">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                            </div>
                            <div class="gallery-info">
                                <span class="gallery-type">
                                    <i class="fas fa-image me-1"></i>صورة
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- عنصر فيديو -->
                    <div class="gallery-video">
                        <video poster="assets/img/video-poster.jpg" preload="metadata">
                            <source src="uploads/media/<?php echo $media['file_url']; ?>" type="video/mp4">
                            متصفحك لا يدعم تشغيل الفيديو.
                        </video>
                        <div class="gallery-overlay">
                            <div class="gallery-actions">
                                <button class="gallery-action-btn" onclick="openGalleryLightbox('uploads/media/<?php echo $media['file_url']; ?>', '<?php echo addslashes($media['title']); ?>', 'video')">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="gallery-action-btn" onclick="shareMedia('<?php echo $media['file_url']; ?>', '<?php echo addslashes($media['title']); ?>')">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                            </div>
                            <div class="gallery-info">
                                <span class="gallery-type">
                                    <i class="fas fa-video me-1"></i>فيديو
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- معلومات العنصر -->
                    <div class="gallery-card-content">
                        <h5 class="gallery-title"><?php echo htmlspecialchars($media['title']); ?></h5>
                        <div class="gallery-meta">
                            <span class="gallery-date">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('d/m/Y', strtotime($media['created_at'])); ?>
                            </span>
                            <span class="gallery-category">
                                <i class="fas fa-tag me-1"></i>
                                <?php
                                switch($category) {
                                    case 'installation': echo 'تركيب'; break;
                                    case 'maintenance': echo 'صيانة'; break;
                                    case 'projects': echo 'مشاريع'; break;
                                    default: echo 'عام'; break;
                                }
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- زر تحميل المزيد -->
        <div class="text-center mt-5">
            <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn" style="display: none;">
                <i class="fas fa-plus me-2"></i>تحميل المزيد
            </button>
        </div>

        <?php endif; ?>
    </div>
</section>

<!-- نافذة منبثقة لعرض الوسائط -->
<div id="mediaLightbox" class="media-lightbox" style="display: none;">
    <div class="lightbox-overlay" onclick="closeLightbox()"></div>
    <div class="lightbox-container">
        <button class="lightbox-close" onclick="closeLightbox()">
            <i class="fas fa-times"></i>
        </button>

        <!-- أزرار التنقل -->
        <button class="lightbox-nav lightbox-prev" onclick="navigateMedia(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="lightbox-nav lightbox-next" onclick="navigateMedia(1)">
            <i class="fas fa-chevron-right"></i>
        </button>

        <!-- محتوى الوسائط -->
        <div class="lightbox-content">
            <div class="lightbox-media">
                <img id="lightboxImage" src="" alt="" style="display: none;">
                <video id="lightboxVideo" controls style="display: none;">
                    <source src="" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
            </div>
            <div class="lightbox-info">
                <h4 id="lightboxTitle"></h4>
                <div class="lightbox-meta">
                    <span id="lightboxType"></span>
                    <span id="lightboxCounter"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم دعوة للعمل -->
<section class="cta-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2>هل تريد أن يكون مشروعك التالي في معرضنا؟</h2>
                <p class="lead">تواصل معنا اليوم للحصول على استشارة مجانية وتصميم نظام الطاقة الشمسية المناسب لاحتياجاتك.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="contact.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-phone me-2"></i>تواصل معنا الآن
                </a>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript للمعرض -->
<script>
// متغيرات عامة
let currentMediaIndex = 0;
let filteredMedia = [];
let allMedia = [];

// تحضير البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeGallery();
    setupFilters();
    setupLightbox();
});

// تهيئة المعرض
function initializeGallery() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    allMedia = Array.from(galleryItems).map((item, index) => {
        const img = item.querySelector('img');
        const video = item.querySelector('video source');
        const title = item.querySelector('.gallery-title').textContent;
        const type = img ? 'image' : 'video';
        const src = img ? img.src : video.src;

        return {
            index: index,
            src: src,
            title: title,
            type: type,
            element: item
        };
    });

    filteredMedia = [...allMedia];
    updateGalleryDisplay();
}

// إعداد أزرار التصفية
function setupFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الكلاس النشط من جميع الأزرار
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // إضافة الكلاس النشط للزر المحدد
            this.classList.add('active');

            // تطبيق التصفية
            const filter = this.getAttribute('data-filter');
            filterGallery(filter);
        });
    });
}

// تصفية المعرض
function filterGallery(filter) {
    const galleryItems = document.querySelectorAll('.gallery-item');

    galleryItems.forEach(item => {
        if (filter === 'all') {
            item.style.display = 'block';
            item.classList.add('show');
        } else {
            const categories = item.getAttribute('data-category');
            if (categories.includes(filter)) {
                item.style.display = 'block';
                item.classList.add('show');
            } else {
                item.style.display = 'none';
                item.classList.remove('show');
            }
        }
    });

    // تحديث المصفوفة المفلترة
    if (filter === 'all') {
        filteredMedia = [...allMedia];
    } else {
        filteredMedia = allMedia.filter(media => {
            const categories = media.element.getAttribute('data-category');
            return categories.includes(filter);
        });
    }

    // إضافة تأثير الظهور
    setTimeout(() => {
        const visibleItems = document.querySelectorAll('.gallery-item.show');
        visibleItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 100);
}

// فتح النافذة المنبثقة
function openGalleryLightbox(src, title, type) {
    const lightbox = document.getElementById('mediaLightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxVideo = document.getElementById('lightboxVideo');
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxType = document.getElementById('lightboxType');
    const lightboxCounter = document.getElementById('lightboxCounter');

    // العثور على الفهرس الحالي
    currentMediaIndex = filteredMedia.findIndex(media => media.src.includes(src));

    // تحديث المحتوى
    updateLightboxContent();

    // إظهار النافذة المنبثقة
    lightbox.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    setTimeout(() => {
        lightbox.style.opacity = '1';
    }, 10);
}

// تحديث محتوى النافذة المنبثقة
function updateLightboxContent() {
    if (currentMediaIndex < 0 || currentMediaIndex >= filteredMedia.length) return;

    const currentMedia = filteredMedia[currentMediaIndex];
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxVideo = document.getElementById('lightboxVideo');
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxType = document.getElementById('lightboxType');
    const lightboxCounter = document.getElementById('lightboxCounter');

    // إخفاء جميع العناصر أولاً
    lightboxImage.style.display = 'none';
    lightboxVideo.style.display = 'none';

    // عرض المحتوى المناسب
    if (currentMedia.type === 'image') {
        lightboxImage.src = currentMedia.src;
        lightboxImage.style.display = 'block';
        lightboxType.innerHTML = '<i class="fas fa-image me-1"></i>صورة';
    } else {
        lightboxVideo.querySelector('source').src = currentMedia.src;
        lightboxVideo.load();
        lightboxVideo.style.display = 'block';
        lightboxType.innerHTML = '<i class="fas fa-video me-1"></i>فيديو';
    }

    // تحديث المعلومات
    lightboxTitle.textContent = currentMedia.title;
    lightboxCounter.textContent = `${currentMediaIndex + 1} من ${filteredMedia.length}`;

    // تحديث أزرار التنقل
    const prevBtn = document.querySelector('.lightbox-prev');
    const nextBtn = document.querySelector('.lightbox-next');

    prevBtn.style.display = currentMediaIndex > 0 ? 'block' : 'none';
    nextBtn.style.display = currentMediaIndex < filteredMedia.length - 1 ? 'block' : 'none';
}

// التنقل بين الوسائط
function navigateMedia(direction) {
    const newIndex = currentMediaIndex + direction;

    if (newIndex >= 0 && newIndex < filteredMedia.length) {
        currentMediaIndex = newIndex;
        updateLightboxContent();
    }
}

// إغلاق النافذة المنبثقة
function closeLightbox() {
    const lightbox = document.getElementById('mediaLightbox');
    const lightboxVideo = document.getElementById('lightboxVideo');

    // إيقاف الفيديو إذا كان يعمل
    if (!lightboxVideo.paused) {
        lightboxVideo.pause();
    }

    // إخفاء النافذة المنبثقة
    lightbox.style.opacity = '0';
    document.body.style.overflow = '';

    setTimeout(() => {
        lightbox.style.display = 'none';
    }, 300);
}

// إعداد النافذة المنبثقة
function setupLightbox() {
    // إغلاق عند الضغط على ESC
    document.addEventListener('keydown', function(e) {
        const lightbox = document.getElementById('mediaLightbox');
        if (lightbox.style.display === 'flex') {
            if (e.key === 'Escape') {
                closeLightbox();
            } else if (e.key === 'ArrowLeft') {
                navigateMedia(1);
            } else if (e.key === 'ArrowRight') {
                navigateMedia(-1);
            }
        }
    });
}

// مشاركة الوسائط
function shareMedia(fileUrl, title) {
    if (navigator.share) {
        navigator.share({
            title: title,
            text: 'شاهد هذا العمل من معرض أعمالنا',
            url: window.location.href
        });
    } else {
        // نسخ الرابط إلى الحافظة
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ الرابط إلى الحافظة');
        });
    }
}

// تحديث عرض المعرض
function updateGalleryDisplay() {
    const galleryItems = document.querySelectorAll('.gallery-item');

    galleryItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}
</script>

<!-- CSS للصور المرنة في معرض الأعمال -->
<style>
/* تحسين عرض الصور في المعرض */
.gallery-image-container {
    background: #f8f9fa;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 250px;
    overflow: hidden;
    position: relative;
    padding: 15px;
}

.gallery-img {
    width: 100%;
    height: auto !important;
    max-height: none !important;
    object-fit: contain !important;
    transition: transform 0.3s ease;
    background: transparent;
    border-radius: 0.375rem;
}

.gallery-img:hover {
    transform: scale(1.02);
}

/* تحسين بطاقات المعرض */
.gallery-card {
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* تحسين overlay */
.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 20px;
}

.gallery-image:hover .gallery-overlay {
    opacity: 1;
}

/* تحسين أزرار الإجراءات */
.gallery-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
    margin-bottom: auto;
}

.gallery-action-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.gallery-action-btn:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

/* تحسين معلومات المعرض */
.gallery-info {
    margin-top: auto;
}

.gallery-type {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* تحسين محتوى البطاقة */
.gallery-card-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.gallery-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.gallery-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    font-size: 0.9rem;
    color: #6c757d;
}

.gallery-date,
.gallery-category {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* تحسين شبكة المعرض */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.gallery-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.gallery-item.show {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين أزرار التصفية */
.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 30px;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #495057;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 0.5rem;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    font-size: 2.5rem;
    color: #007bff;
    margin-bottom: 15px;
}

.stats-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stats-content p {
    color: #6c757d;
    font-weight: 600;
    margin: 0;
}

/* تحسينات الموبايل */
@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .gallery-image-container {
        min-height: 200px;
        padding: 10px;
    }

    .gallery-card-content {
        padding: 15px;
    }

    .filter-buttons {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 100%;
        max-width: 250px;
    }

    .stats-card {
        padding: 20px 15px;
    }

    .stats-icon {
        font-size: 2rem;
    }

    .stats-content h3 {
        font-size: 1.5rem;
    }
}

/* تحسين إضافي للصور */
.gallery-img {
    /* إزالة أي قيود على الارتفاع */
    max-height: none !important;
    /* السماح للصورة بأن تأخذ حجمها الطبيعي */
    height: auto !important;
    /* ضمان عدم تشويه الصورة */
    object-fit: contain !important;
}

/* تحسين النافذة المنبثقة */
.media-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lightbox-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
}

.lightbox-media img {
    width: 100%;
    height: auto !important;
    max-height: 80vh;
    object-fit: contain !important;
    background: #f8f9fa;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 10000;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    background: #dc3545;
    transform: scale(1.1);
}

/* تحسين أزرار التنقل */
.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10000;
}

.lightbox-prev {
    left: 15px;
    border-radius: 0 25px 25px 0;
}

.lightbox-next {
    right: 15px;
    border-radius: 25px 0 0 25px;
}

.lightbox-nav:hover {
    background: #007bff;
    transform: translateY(-50%) scale(1.1);
}
</style>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
