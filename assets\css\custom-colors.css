/* ملف CSS المخصص للألوان - تم إنشاؤه تلقائيًا */

:root {
    --primary-color: #c86808;
    --secondary-color: #e87a09;
    --slider-title-color: #ffffff;
    --slider-subtitle-color: #ffffff;
}

.brand-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.modern-navbar {
    border-bottom: 3px solid var(--primary-color);
}

.nav-icon {
    color: var(--primary-color);
}

.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.navbar-light .navbar-nav .nav-link.active .nav-icon {
    color: var(--primary-color);
}

/* تخصيص ألوان السلايدر */
.animated-heading {
    color: var(--slider-title-color) !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: none !important;
    -webkit-background-clip: initial !important;
    -webkit-text-fill-color: var(--slider-title-color) !important;
    background-clip: initial !important;
}

/* إزالة أي تنسيقات متدرجة سابقة */
.animated-heading {
    background-image: none !important;
}

.animated-text {
    color: var(--slider-subtitle-color) !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* تنسيقات الوضع الليلي */
[data-theme="dark"] .animated-heading {
    color: var(--slider-title-color) !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    -webkit-text-fill-color: var(--slider-title-color) !important;
}

[data-theme="dark"] .animated-text {
    color: var(--slider-subtitle-color) !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

/* تطبيق الألوان على العناصر المضمنة - أولوية قصوى */
h1.animated-heading[style*="color"],
h1.display-4.fw-bold.mb-4.animated-heading,
.carousel-item h1.animated-heading,
.hero-banner h1.animated-heading {
    color: var(--slider-title-color) !important;
    -webkit-text-fill-color: var(--slider-title-color) !important;
}

p.animated-text[style*="color"],
p.fs-4.mb-4.animated-text,
.carousel-item p.animated-text,
.hero-banner p.animated-text {
    color: var(--slider-subtitle-color) !important;
}

[data-theme="dark"] h1.animated-heading[style*="color"],
[data-theme="dark"] h1.display-4.fw-bold.mb-4.animated-heading,
[data-theme="dark"] .carousel-item h1.animated-heading,
[data-theme="dark"] .hero-banner h1.animated-heading {
    color: var(--slider-title-color) !important;
    -webkit-text-fill-color: var(--slider-title-color) !important;
}

[data-theme="dark"] p.animated-text[style*="color"],
[data-theme="dark"] p.fs-4.mb-4.animated-text,
[data-theme="dark"] .carousel-item p.animated-text,
[data-theme="dark"] .hero-banner p.animated-text {
    color: var(--slider-subtitle-color) !important;
}
