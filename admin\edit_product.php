<?php
$page_title = 'تعديل المنتج';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

$error = '';
$success = '';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: products.php');
    exit;
}

$product_id = $_GET['id'];

// الحصول على بيانات المنتج
$stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
$stmt->execute([$product_id]);
$product = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$product) {
    header('Location: products.php');
    exit;
}

// الحصول على الصور الإضافية للمنتج
$additional_images_stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ?");
$additional_images_stmt->execute([$product_id]);
$additional_images = $additional_images_stmt->fetchAll(PDO::FETCH_ASSOC);

// معالجة نموذج تعديل المنتج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات المدخلة
    $name = clean_input($_POST['name']);
    $category = clean_input($_POST['category']);
    $description = clean_input($_POST['description']);
    $price = clean_input($_POST['price']);

    if (empty($name) || empty($category) || empty($description) || empty($price)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!is_numeric($price) || $price <= 0) {
        $error = 'يرجى إدخال سعر صحيح';
    } else {
        // بدء المعاملة
        $pdo->beginTransaction();

        try {
            // التحقق مما إذا تم تحميل صورة رئيسية جديدة
            if ($_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
                // رفع الصورة الجديدة
                $upload_dir = '../uploads/products/';

                // التأكد من وجود المجلد
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $upload_result = upload_file($_FILES['image'], $upload_dir);

                if ($upload_result['success']) {
                    // حذف الصورة القديمة
                    $old_image_path = $upload_dir . $product['image'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }

                    // تحديث المنتج مع الصورة الجديدة
                    $stmt = $pdo->prepare("UPDATE products SET name = ?, image = ?, category = ?, description = ?, price = ? WHERE id = ?");
                    $result = $stmt->execute([$name, $upload_result['filename'], $category, $description, $price, $product_id]);
                } else {
                    throw new Exception($upload_result['message']);
                }
            } else {
                // تحديث المنتج بدون تغيير الصورة
                $stmt = $pdo->prepare("UPDATE products SET name = ?, category = ?, description = ?, price = ? WHERE id = ?");
                $result = $stmt->execute([$name, $category, $description, $price, $product_id]);
            }

            // حذف الصور المحددة
            if (isset($_POST['delete_images']) && is_array($_POST['delete_images'])) {
                $upload_dir = '../uploads/products/';

                foreach ($_POST['delete_images'] as $image_id) {
                    // الحصول على معلومات الصورة
                    $img_stmt = $pdo->prepare("SELECT image_url FROM product_images WHERE id = ? AND product_id = ?");
                    $img_stmt->execute([$image_id, $product_id]);
                    $image = $img_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($image) {
                        // حذف الصورة من المجلد
                        $image_path = $upload_dir . $image['image_url'];
                        if (file_exists($image_path)) {
                            unlink($image_path);
                        }

                        // حذف الصورة من قاعدة البيانات
                        $delete_stmt = $pdo->prepare("DELETE FROM product_images WHERE id = ?");
                        $delete_stmt->execute([$image_id]);
                    }
                }
            }

            // رفع الصور الإضافية الجديدة إذا وجدت
            if (isset($_FILES['additional_images']) && !empty($_FILES['additional_images']['name'][0])) {
                $upload_dir = '../uploads/products/';
                $additional_images_count = count($_FILES['additional_images']['name']);

                for ($i = 0; $i < $additional_images_count; $i++) {
                    if ($_FILES['additional_images']['error'][$i] === UPLOAD_ERR_OK) {
                        // إنشاء مصفوفة للملف الحالي
                        $current_file = [
                            'name' => $_FILES['additional_images']['name'][$i],
                            'type' => $_FILES['additional_images']['type'][$i],
                            'tmp_name' => $_FILES['additional_images']['tmp_name'][$i],
                            'error' => $_FILES['additional_images']['error'][$i],
                            'size' => $_FILES['additional_images']['size'][$i]
                        ];

                        $additional_upload_result = upload_file($current_file, $upload_dir);

                        if ($additional_upload_result['success']) {
                            // إضافة الصورة إلى جدول صور المنتج
                            $img_stmt = $pdo->prepare("INSERT INTO product_images (product_id, image_url) VALUES (?, ?)");
                            $img_stmt->execute([$product_id, $additional_upload_result['filename']]);
                        }
                    }
                }
            }

            // تأكيد المعاملة
            $pdo->commit();

            $success = 'تم تحديث المنتج بنجاح';

            // إعادة تحميل بيانات المنتج
            $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);

            // إعادة تحميل الصور الإضافية
            $additional_images_stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ?");
            $additional_images_stmt->execute([$product_id]);
            $additional_images = $additional_images_stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث المنتج: ' . $e->getMessage();
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديل المنتج</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="products.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المنتجات
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php echo $error; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (!empty($success)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <?php echo $success; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<!-- Edit product form -->
<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">تعديل المنتج: <?php echo htmlspecialchars($product['name']); ?></h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="category" class="form-label">القسم <span class="text-danger">*</span></label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر القسم</option>
                        <option value="panel" <?php echo ($product['category'] == 'panel') ? 'selected' : ''; ?>>ألواح شمسية</option>
                        <option value="inverter" <?php echo ($product['category'] == 'inverter') ? 'selected' : ''; ?>>إنفرترات</option>
                        <option value="battery" <?php echo ($product['category'] == 'battery') ? 'selected' : ''; ?>>بطاريات</option>
                        <option value="accessory" <?php echo ($product['category'] == 'accessory') ? 'selected' : ''; ?>>إكسسوارات</option>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">وصف المنتج <span class="text-danger">*</span></label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($product['description']); ?></textarea>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="price" class="form-label">السعر (د.ع) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $product['price']; ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="image" class="form-label">الصورة الرئيسية للمنتج</label>
                    <input type="file" class="form-control image-upload" id="image" name="image" accept="image/*" data-preview="image-preview">
                    <small class="form-text text-muted">اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الصورة الرئيسية</small>
                    <div class="mt-2">
                        <img id="image-preview" src="../uploads/products/<?php echo $product['image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-thumbnail" style="max-height: 200px;">
                    </div>
                </div>
            </div>

            <!-- الصور الإضافية الحالية -->
            <?php if (!empty($additional_images)): ?>
            <div class="mb-3">
                <label class="form-label">الصور الإضافية الحالية</label>
                <div class="row g-2">
                    <?php foreach ($additional_images as $image): ?>
                    <div class="col-4 col-md-3 col-lg-2">
                        <div class="card h-100">
                            <img src="../uploads/products/<?php echo $image['image_url']; ?>" class="card-img-top" alt="صورة إضافية" style="height: 100px; object-fit: cover;">
                            <div class="card-body p-2 text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="delete_images[]" value="<?php echo $image['id']; ?>" id="delete-image-<?php echo $image['id']; ?>">
                                    <label class="form-check-label" for="delete-image-<?php echo $image['id']; ?>">
                                        حذف
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <small class="form-text text-muted">حدد الصور التي ترغب في حذفها</small>
            </div>
            <?php endif; ?>

            <!-- إضافة صور جديدة -->
            <div class="mb-3">
                <label for="additional_images" class="form-label">إضافة صور جديدة</label>
                <input type="file" class="form-control" id="additional_images" name="additional_images[]" accept="image/*" multiple>
                <small class="form-text text-muted">يمكنك اختيار عدة صور في نفس الوقت (اختياري)</small>
                <div id="additional-images-preview" class="mt-2 row g-2"></div>
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="products.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
