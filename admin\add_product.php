<?php
$page_title = 'إضافة منتج جديد';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// التحقق من تسجيل دخول المدير
redirect_if_not_logged_in();

// دالة للتحقق من وجود جدول في قاعدة البيانات
function tableExists($pdo, $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '{$table}'");
        return $result->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// التأكد من وجود مجلد الصور
$upload_dir = "../uploads/products_images";
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// تحديد قائمة الأقسام المتاحة
$categories = [
    ['id' => 'panel', 'name' => 'ألواح شمسية'],
    ['id' => 'inverter', 'name' => 'انفرترات'],
    ['id' => 'battery', 'name' => 'بطاريات'],
    ['id' => 'accessories', 'name' => 'إكسسوارات']
];

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // استلام البيانات من النموذج
    $category = clean_input($_POST['category_id']); // استخدام category بدلاً من category_id
    $name = clean_input($_POST['name']);
    $description = $_POST['description']; // لا نستخدم clean_input هنا لأنه يحتوي على HTML
    $price = (float)$_POST['price'];

    // التحقق من البيانات المطلوبة
    if (empty($name) || empty($category)) {
        $_SESSION['error_message'] = "يرجى إدخال اسم المنتج واختيار القسم";
    } else {
        // إضافة المنتج إلى قاعدة البيانات
        $stmt = $pdo->prepare("
            INSERT INTO products (
                name, image, category,
                description, price, created_at
            ) VALUES (
                ?, 'placeholder.jpg', ?,
                ?, ?, NOW()
            )
        ");

        $result = $stmt->execute([
            $name, $category,
            $description, $price
        ]);

        if ($result) {
            $product_id = $pdo->lastInsertId();

            // معالجة الصورة الرئيسية
            $upload_errors = [];

            // التحقق من وجود صورة
            if (isset($_FILES["image1"]) && $_FILES["image1"]['error'] == 0) {
                $allowed_types = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                $max_size = 5 * 1024 * 1024; // 5 ميجابايت

                if (!in_array($_FILES["image1"]['type'], $allowed_types)) {
                    $upload_errors[] = "نوع الملف غير مسموح به. يرجى رفع صورة بصيغة JPEG أو PNG أو GIF";
                } elseif ($_FILES["image1"]['size'] > $max_size) {
                    $upload_errors[] = "حجم الصورة كبير جدًا. الحد الأقصى هو 5 ميجابايت";
                } else {
                    // إنشاء اسم فريد للصورة
                    $image_name = uniqid() . '_' . $_FILES["image1"]['name'];
                    $upload_path = $upload_dir . '/' . $image_name;

                    // نقل الصورة إلى المجلد المحدد
                    if (move_uploaded_file($_FILES["image1"]['tmp_name'], $upload_path)) {
                        // تحديث المنتج بالصورة الجديدة
                        $stmt = $pdo->prepare("UPDATE products SET image = ? WHERE id = ?");
                        $stmt->execute([$image_name, $product_id]);

                        // إضافة الصورة إلى جدول صور المنتجات إذا كان موجودًا
                        if (tableExists($pdo, 'product_images')) {
                            $stmt = $pdo->prepare("
                                INSERT INTO product_images (
                                    product_id, image_url, created_at
                                ) VALUES (
                                    ?, ?, NOW()
                                )
                            ");

                            $stmt->execute([
                                $product_id, $image_name
                            ]);
                        }
                    } else {
                        $upload_errors[] = "فشل في رفع الصورة";
                    }
                }
            }

            // لا نحتاج إلى معالجة خصائص المنتج في هذه النسخة المبسطة

            if (!empty($upload_errors)) {
                $_SESSION['warning_message'] = "تم إضافة المنتج بنجاح، ولكن هناك بعض المشاكل في رفع الصور: " . implode(", ", $upload_errors);
            } else {
                $_SESSION['success_message'] = "تم إضافة المنتج بنجاح";
            }

            header("Location: products.php");
            exit;
        } else {
            $_SESSION['error_message'] = "حدث خطأ أثناء إضافة المنتج";
        }
    }
}

// تضمين ملف الرأس
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">إضافة منتج جديد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="products.php" class="btn btn-sm btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المنتجات
        </a>
    </div>
</div>

<?php if (isset($_SESSION['error_message'])): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <?php
    echo $_SESSION['error_message'];
    unset($_SESSION['error_message']);
    ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="card admin-card">
    <div class="card-header">
        <h5 class="mb-0">معلومات المنتج</h5>
    </div>
    <div class="card-body">
        <form action="" method="post" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف التفصيلي <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="6" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">السعر <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                    <span class="input-group-text">د.ع</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">القسم <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">-- اختر القسم --</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">صور المنتج (حتى 3 صور)</label>
                        <div class="mb-2">
                            <label for="image1" class="form-label">الصورة 1 (الرئيسية) <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="image1" name="image1" required>
                            <div class="form-text">يفضل رفع صورة بأبعاد 800×600 بكسل</div>
                        </div>
                        <div class="mb-2">
                            <label for="image2" class="form-label">الصورة 2</label>
                            <input type="file" class="form-control" id="image2" name="image2">
                        </div>
                        <div class="mb-2">
                            <label for="image3" class="form-label">الصورة 3</label>
                            <input type="file" class="form-control" id="image3" name="image3">
                        </div>
                        <div class="form-text">اختر الصورة الرئيسية:</div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="main_image" id="main_image1" value="1" checked>
                            <label class="form-check-label" for="main_image1">1</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="main_image" id="main_image2" value="2">
                            <label class="form-check-label" for="main_image2">2</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="main_image" id="main_image3" value="3">
                            <label class="form-check-label" for="main_image3">3</label>
                        </div>
                    </div>
                </div>

                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ المنتج
                    </button>
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- سكريبت معاينة الصور -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة سكريبت لمعاينة الصور هنا إذا لزم الأمر
    });
</script>

<?php
// تضمين ملف التذييل
require_once 'includes/footer.php';
?>
