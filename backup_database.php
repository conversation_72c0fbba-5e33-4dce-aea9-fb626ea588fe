<?php
/**
 * ملف النسخ الاحتياطي لقاعدة البيانات
 * استخدم هذا الملف لإنشاء نسخة احتياطية من قاعدة البيانات
 * احذف هذا الملف بعد الانتهاء من الإعداد لأسباب أمنية
 */

// التحقق من الوصول المباشر
if (!isset($_GET['backup']) || $_GET['backup'] !== 'confirm') {
    die('وصول غير مصرح به');
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'includes/db_connect.php';

// إعدادات النسخ الاحتياطي
$backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
$backup_path = 'backups/';

// إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
if (!file_exists($backup_path)) {
    mkdir($backup_path, 0755, true);
}

try {
    // الحصول على قائمة الجداول
    $tables = [];
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }

    // بدء محتوى النسخة الاحتياطية
    $backup_content = "-- نسخة احتياطية لقاعدة البيانات\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";
    $backup_content .= "-- موقع شركة بريق ضوء الشمس\n\n";
    $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

    // نسخ كل جدول
    foreach ($tables as $table) {
        // هيكل الجدول
        $result = $pdo->query("SHOW CREATE TABLE `$table`");
        $row = $result->fetch(PDO::FETCH_NUM);
        
        $backup_content .= "-- هيكل الجدول `$table`\n";
        $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup_content .= $row[1] . ";\n\n";

        // بيانات الجدول
        $result = $pdo->query("SELECT * FROM `$table`");
        $num_rows = $result->rowCount();
        
        if ($num_rows > 0) {
            $backup_content .= "-- بيانات الجدول `$table`\n";
            
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $backup_content .= "INSERT INTO `$table` (";
                $backup_content .= "`" . implode("`, `", array_keys($row)) . "`";
                $backup_content .= ") VALUES (";
                
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . addslashes($value) . "'";
                    }
                }
                $backup_content .= implode(", ", $values);
                $backup_content .= ");\n";
            }
            $backup_content .= "\n";
        }
    }

    $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";

    // حفظ النسخة الاحتياطية
    $full_path = $backup_path . $backup_file;
    if (file_put_contents($full_path, $backup_content)) {
        $file_size = formatBytes(filesize($full_path));
        
        // إرسال الملف للتحميل
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $backup_file . '"');
        header('Content-Length: ' . filesize($full_path));
        readfile($full_path);
        
        // حذف الملف المؤقت
        unlink($full_path);
        exit;
    } else {
        throw new Exception('فشل في حفظ النسخة الاحتياطية');
    }

} catch (Exception $e) {
    die('خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage());
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
?>
