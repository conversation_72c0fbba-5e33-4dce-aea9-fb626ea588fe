<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل دخول المدير
if (!isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// تحديد الصفحة النشطة
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - ضوء الشمس</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <!-- Custom Colors CSS -->
    <link rel="stylesheet" href="../assets/css/custom-colors.css">
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin-style.css">
    <!-- تطبيق الوضع الليلي/النهاري قبل تحميل الصفحة لمنع الوميض -->
    <script>
        // التحقق من وجود تفضيل محفوظ
        const currentTheme = localStorage.getItem('theme');
        if (currentTheme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        }
    </script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3 sticky-top">
                    <a href="dashboard.php" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <img src="../assets/img/logo.png" alt="شعار ضوء الشمس" class="me-2" width="40" height="40" onerror="this.src='../assets/img/default-logo.png'; this.onerror='';">
                        <span class="fs-4">ضوء الشمس</span>
                    </a>
                    <hr>
                    <ul class="nav nav-pills flex-column mb-auto">
                        <!-- لوحة التحكم -->
                        <li class="nav-item">
                            <a href="dashboard.php" class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                            </a>
                        </li>

                        <li class="sidebar-heading mt-3 mb-1 text-uppercase text-white-50 small px-3">المحتوى</li>

                        <!-- المنتجات -->
                        <li>
                            <a href="products.php" class="nav-link <?php echo ($current_page == 'products.php' || $current_page == 'add_product.php' || $current_page == 'edit_product.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-solar-panel me-2"></i> المنتجات
                            </a>
                        </li>

                        <!-- البكجات -->
                        <li>
                            <a href="packages.php" class="nav-link <?php echo ($current_page == 'packages.php' || $current_page == 'add_package.php' || $current_page == 'edit_package.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-box me-2"></i> البكجات
                            </a>
                        </li>

                        <!-- معلومات البكجات الإضافية -->
                        <li>
                            <a href="package_additional_info.php" class="nav-link <?php echo ($current_page == 'package_additional_info.php' || $current_page == 'add_package_info.php' || $current_page == 'edit_package_info.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-info-circle me-2"></i> معلومات البكجات
                            </a>
                        </li>

                        <!-- معرض الأعمال -->
                        <li>
                            <a href="gallery-management.php" class="nav-link <?php echo ($current_page == 'gallery-management.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-images me-2"></i> معرض الأعمال
                            </a>
                        </li>

                        <!-- فريق العمل -->
                        <li>
                            <a href="team.php" class="nav-link <?php echo ($current_page == 'team.php' || $current_page == 'add_team_member.php' || $current_page == 'edit_team_member.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-users me-2"></i> فريق العمل
                            </a>
                        </li>

                        <!-- إنجازات الفريق -->
                        <li>
                            <a href="team-achievements.php" class="nav-link <?php echo ($current_page == 'team-achievements.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-trophy me-2"></i> إنجازات الفريق
                            </a>
                        </li>

                        <!-- الشهادات والاعتمادات -->
                        <li>
                            <a href="certificates.php" class="nav-link <?php echo ($current_page == 'certificates.php' || $current_page == 'add_certificate.php' || $current_page == 'edit_certificate.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-certificate me-2"></i> الشهادات والاعتمادات
                            </a>
                        </li>

                        <!-- صور العرض الرئيسية -->
                        <li>
                            <a href="sliders.php" class="nav-link <?php echo ($current_page == 'sliders.php' || $current_page == 'add_slider.php' || $current_page == 'edit_slider.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-sliders-h me-2"></i> صور العرض الرئيسية
                            </a>
                        </li>

                        <li class="sidebar-heading mt-3 mb-1 text-uppercase text-white-50 small px-3">الإعدادات</li>

                        <!-- معلومات الشركة -->
                        <li>
                            <a href="company.php" class="nav-link <?php echo ($current_page == 'company.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-building me-2"></i> معلومات الشركة
                            </a>
                        </li>

                        <!-- إعدادات الموقع -->
                        <li>
                            <a href="site_settings.php" class="nav-link <?php echo ($current_page == 'site_settings.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-cog me-2"></i> إعدادات الموقع
                            </a>
                        </li>

                        <!-- إعدادات صفحة التواصل -->
                        <li>
                            <a href="contact_settings.php" class="nav-link <?php echo ($current_page == 'contact_settings.php') ? 'active' : 'text-white'; ?>">
                                <i class="fas fa-envelope me-2"></i> إعدادات صفحة التواصل
                            </a>
                        </li>
                    </ul>
                    <hr>
                    <div class="d-flex align-items-center">
                        <!-- زر تبديل الوضع الليلي/النهاري -->
                        <div class="navbar-theme-toggle me-3" id="theme-toggle" title="تبديل الوضع الليلي/النهاري">
                            <i class="fas fa-moon fa-lg"></i>
                        </div>

                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-2 fa-2x"></i>
                                <strong><?php echo $_SESSION['admin_username']; ?></strong>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                                <li><a class="dropdown-item" href="../index.php" target="_blank">عرض الموقع</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-link text-dark d-lg-none me-2" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0"><?php echo isset($page_title) ? $page_title : 'لوحة التحكم'; ?></h1>
                    </div>
                    <div class="d-flex align-items-center">
                        <a href="../index.php" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                            <i class="fas fa-external-link-alt"></i> عرض الموقع
                        </a>
                        <a href="logout.php" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>

                <!-- زر العودة للأعلى -->
                <button id="back-to-top" class="btn btn-primary rounded-circle">
                    <i class="fas fa-arrow-up"></i>
                </button>

                <style>
                #back-to-top {
                    position: fixed;
                    bottom: 25px;
                    left: 25px;
                    display: none;
                    width: 50px;
                    height: 50px;
                    text-align: center;
                    line-height: 50px;
                    border: none;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                    z-index: 99;
                    font-size: 18px;
                    transition: all 0.3s ease;
                }
                #back-to-top:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
                }
                </style>