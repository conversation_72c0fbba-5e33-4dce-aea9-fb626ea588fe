<?php
require_once 'includes/header.php';

// التحقق من وجود جدول فريق العمل
try {
    // استعلام للحصول على أعضاء فريق العمل النشطين
    $team_stmt = $pdo->query("SELECT * FROM team_members WHERE status = 1 ORDER BY id ASC");
    $team_members = $team_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // إذا لم يكن الجدول موجودًا، قم بإنشاء مصفوفة فارغة
    $team_members = [];
}

// استعلام للحصول على الشهادات النشطة
try {
    $certificates_stmt = $pdo->query("SELECT * FROM certificates WHERE status = 1 ORDER BY id ASC");
    $certificates = $certificates_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // إذا لم يكن الجدول موجودًا، قم بإنشاء مصفوفة فارغة
    $certificates = [];
}

// استعلام للحصول على إنجازات الفريق
try {
    $achievements_stmt = $pdo->query("SELECT * FROM team_achievements WHERE status = 1 ORDER BY type, sort_order");
    $achievements = $achievements_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم الإنجازات حسب النوع
    $team_achievements = [];
    $team_expertise = [];
    foreach ($achievements as $achievement) {
        if ($achievement['type'] == 'achievement') {
            $team_achievements[] = $achievement;
        } else {
            $team_expertise[] = $achievement;
        }
    }

    // استعلام للحصول على إعدادات الأقسام
    $settings_stmt = $pdo->query("SELECT * FROM team_section_settings");
    $settings = $settings_stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم الإعدادات
    $section_settings = [];
    foreach ($settings as $setting) {
        $section_settings[$setting['section_name']] = $setting;
    }
} catch (Exception $e) {
    // في حالة عدم وجود الجداول، استخدام البيانات الافتراضية
    $team_achievements = [
        ['title' => 'مشروع منجز', 'value' => '+500', 'description' => 'قمنا بتنفيذ أكثر من 500 مشروع طاقة شمسية بنجاح في مختلف المحافظات', 'icon' => 'fas fa-project-diagram'],
        ['title' => 'عميل راضٍ', 'value' => '+1000', 'description' => 'أكثر من 1000 عميل راضٍ عن خدماتنا ويوصون بنا لأصدقائهم وعائلاتهم', 'icon' => 'fas fa-users'],
        ['title' => 'سنوات خبرة', 'value' => '+15', 'description' => 'نمتلك خبرة تزيد عن 15 عاماً في مجال تصميم وتركيب أنظمة الطاقة الشمسية', 'icon' => 'fas fa-award']
    ];

    $team_expertise = [
        ['title' => 'تصميم الأنظمة', 'percentage' => 95, 'color' => 'primary'],
        ['title' => 'تركيب الأنظمة', 'percentage' => 90, 'color' => 'success'],
        ['title' => 'خدمة العملاء', 'percentage' => 98, 'color' => 'info']
    ];

    $section_settings = [
        'achievements' => [
            'title' => 'خبرة وكفاءة',
            'subtitle' => 'إنجازاتنا',
            'description' => 'نفخر بإنجازاتنا وخبراتنا المتراكمة في مجال الطاقة الشمسية',
            'main_text' => 'فريق متكامل من الخبراء',
            'secondary_text' => 'يتكون فريقنا من مهندسين متخصصين في تصميم أنظمة الطاقة الشمسية وفنيين محترفين في تركيب وصيانة هذه الأنظمة.'
        ],
        'expertise_description' => [
            'description' => 'نحرص على تدريب فريقنا باستمرار على أحدث التقنيات والمعايير العالمية لضمان تقديم أفضل الخدمات لعملائنا. يمتلك فريقنا خبرة تزيد عن 10 سنوات في مجال الطاقة الشمسية.'
        ]
    ];
}
?>

<!-- عنوان الصفحة البسيط -->
<div class="container mt-5">
    <div class="row">
        <div class="col-lg-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-4">فريقنا المتخصص</h1>
        </div>
    </div>
</div>

<!-- قسم فريق العمل المحسن -->
<section class="team-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-7 text-center">
                <div class="section-title">
                    <span class="subtitle">تعرف علينا</span>
                    <p class="section-description">نفتخر بفريقنا المتخصص من المهندسين والفنيين ذوي الخبرة في مجال الطاقة الشمسية</p>
                </div>
            </div>
        </div>

        <!-- أعضاء الفريق - محسن -->
        <div class="row">
            <?php if (empty($team_members)): ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <h4>لا يوجد أعضاء في فريق العمل حالياً</h4>
                    <p>سيتم إضافة أعضاء الفريق قريباً.</p>
                </div>
            </div>
            <?php else: ?>
                <?php
                $delay = 0;
                foreach ($team_members as $index => $member):
                    // الحصول على مهارات العضو
                    $skills = [];
                    try {
                        $skills_stmt = $pdo->prepare("SELECT * FROM team_member_skills WHERE member_id = ?");
                        $skills_stmt->execute([$member['id']]);
                        $skills = $skills_stmt->fetchAll(PDO::FETCH_ASSOC);
                    } catch (PDOException $e) {
                        // تجاهل الخطأ إذا لم يكن الجدول موجودًا
                    }
                ?>
                <div class="col-lg-3 col-md-6 mb-5 animate-on-scroll" <?php echo $delay > 0 ? 'data-delay="'.$delay.'"' : ''; ?>>
                    <div class="team-member-card enhanced">
                        <div class="member-image">
                            <?php if (!empty($member['image']) && file_exists("uploads/team/" . $member['image'])): ?>
                            <img src="uploads/team/<?php echo $member['image']; ?>" alt="<?php echo $member['name']; ?>" class="img-fluid">
                            <?php else: ?>
                            <img src="assets/img/team-member<?php echo ($index % 4) + 1; ?>.jpg" alt="<?php echo $member['name']; ?>" class="img-fluid">
                            <?php endif; ?>
                            <div class="member-overlay">
                                <div class="member-details">
                                    <p><?php echo !empty($member['bio']) ? substr($member['bio'], 0, 100) . (strlen($member['bio']) > 100 ? '...' : '') : 'خبرة في مجال الطاقة الشمسية'; ?></p>
                                    <div class="member-skills">
                                        <?php if (!empty($skills)): ?>
                                            <?php foreach (array_slice($skills, 0, 2) as $skill): ?>
                                            <div class="skill-item">
                                                <span class="skill-label"><?php echo $skill['skill_name']; ?></span>
                                                <div class="skill-bar">
                                                    <div class="skill-progress" style="width: <?php echo $skill['skill_percentage']; ?>%"></div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="skill-item">
                                                <span class="skill-label">خبرة مهنية</span>
                                                <div class="skill-bar">
                                                    <div class="skill-progress" style="width: 90%"></div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="social-icons">
                                <?php if (!empty($member['facebook'])): ?>
                                <a href="<?php echo $member['facebook']; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($member['twitter'])): ?>
                                <a href="<?php echo $member['twitter']; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($member['linkedin'])): ?>
                                <a href="<?php echo $member['linkedin']; ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($member['instagram'])): ?>
                                <a href="<?php echo $member['instagram']; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="member-info">
                            <?php if (!empty($member['badge'])): ?>
                            <div class="member-badge"><?php echo $member['badge']; ?></div>
                            <?php endif; ?>
                            <h4><?php echo $member['name']; ?></h4>
                            <p class="position"><?php echo $member['position']; ?></p>
                            <?php if (!empty($member['quote'])): ?>
                            <p class="member-quote">"<?php echo $member['quote']; ?>"</p>
                            <?php endif; ?>
                            <button class="btn btn-sm btn-outline-primary rounded-pill mt-2" onclick="openCustomModal('memberModal<?php echo $member['id']; ?>')">
                                <i class="fas fa-user-circle me-1"></i> عرض السيرة الذاتية
                            </button>
                        </div>
                    </div>
                </div>
                <?php
                    $delay += 200;
                endforeach;
                ?>
            <?php endif; ?>
        </div>

        <!-- النوافذ المنبثقة للسير الذاتية -->
        <?php if (!empty($team_members)): ?>
            <?php foreach ($team_members as $member):
                // الحصول على مهارات العضو
                $skills = [];
                try {
                    $skills_stmt = $pdo->prepare("SELECT * FROM team_member_skills WHERE member_id = ?");
                    $skills_stmt->execute([$member['id']]);
                    $skills = $skills_stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    // تجاهل الخطأ إذا لم يكن الجدول موجودًا
                }

                // الحصول على شهادات العضو
                $certifications = [];
                try {
                    $certifications_stmt = $pdo->prepare("SELECT * FROM team_member_certifications WHERE member_id = ?");
                    $certifications_stmt->execute([$member['id']]);
                    $certifications = $certifications_stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    // تجاهل الخطأ إذا لم يكن الجدول موجودًا
                }
            ?>
            <!-- استخدام نافذة منبثقة مخصصة بدلاً من Bootstrap Modal - محسنة ومنظمة -->
            <div class="custom-modal" id="memberModal<?php echo $member['id']; ?>" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); z-index: 9999; overflow-y: auto; padding: 0; direction: rtl;">
                <div class="custom-modal-dialog" style="position: relative; width: 90%; max-width: 900px; margin: 100px auto 30px; background-color: white; border-radius: 10px; box-shadow: 0 10px 25px rgba(0,0,0,0.5); overflow: hidden;">
                    <!-- زر إغلاق كبير في الزاوية -->
                    <button type="button" class="btn-close-custom" onclick="closeCustomModal('memberModal<?php echo $member['id']; ?>')" style="position: absolute; top: 15px; right: 15px; background: rgba(255,255,255,0.8); border: none; width: 35px; height: 35px; border-radius: 50%; font-size: 1.5rem; cursor: pointer; color: #dc3545; z-index: 10; display: flex; align-items: center; justify-content: center;">&times;</button>

                    <!-- رأس النافذة المنبثقة مع خلفية ملونة -->
                    <div class="custom-modal-header" style="padding: 15px; background: linear-gradient(135deg, #007bff, #0056b3); color: white; text-align: center; position: relative;">
                        <h4 class="modal-title" style="margin: 0; font-weight: bold;">السيرة الذاتية - <?php echo $member['name']; ?></h4>
                    </div>

                    <!-- جسم النافذة المنبثقة - تصميم أفقي أكثر كفاءة -->
                    <div class="custom-modal-body" style="padding: 20px;">
                        <div class="row align-items-start">
                            <!-- معلومات أساسية في الأعلى -->
                            <div class="col-12 mb-3">
                                <div class="d-flex flex-wrap align-items-center" style="background-color: #f8f9fa; border-radius: 10px; padding: 15px;">
                                    <!-- صورة العضو -->
                                    <div class="me-4 text-center" style="width: 120px;">
                                        <?php if (!empty($member['image']) && file_exists("uploads/team/" . $member['image'])): ?>
                                        <img src="uploads/team/<?php echo $member['image']; ?>" alt="<?php echo $member['name']; ?>" class="img-fluid rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                                        <?php else: ?>
                                        <img src="assets/img/team-member<?php echo (array_search($member, $team_members) % 4) + 1; ?>.jpg" alt="<?php echo $member['name']; ?>" class="img-fluid rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);">
                                        <?php endif; ?>
                                    </div>

                                    <!-- معلومات العضو الأساسية -->
                                    <div class="flex-grow-1">
                                        <h5 style="color: #007bff; margin-bottom: 5px;"><?php echo $member['name']; ?></h5>
                                        <p class="text-primary mb-2" style="font-weight: 500;"><?php echo $member['position']; ?></p>

                                        <div class="d-flex flex-wrap align-items-center">
                                            <?php if (!empty($member['experience_years'])): ?>
                                            <span class="badge bg-primary me-2 mb-2" style="font-size: 0.85rem; border-radius: 30px; padding: 5px 10px;">
                                                <i class="fas fa-star me-1"></i> خبرة <?php echo $member['experience_years']; ?> سنة
                                            </span>
                                            <?php endif; ?>

                                            <!-- وسائل التواصل الاجتماعي -->
                                            <div class="member-social mb-2" style="display: flex; gap: 5px;">
                                                <?php if (!empty($member['facebook'])): ?>
                                                <a href="<?php echo $member['facebook']; ?>" target="_blank" class="btn btn-sm btn-outline-primary rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;"><i class="fab fa-facebook-f"></i></a>
                                                <?php endif; ?>
                                                <?php if (!empty($member['twitter'])): ?>
                                                <a href="<?php echo $member['twitter']; ?>" target="_blank" class="btn btn-sm btn-outline-info rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;"><i class="fab fa-twitter"></i></a>
                                                <?php endif; ?>
                                                <?php if (!empty($member['linkedin'])): ?>
                                                <a href="<?php echo $member['linkedin']; ?>" target="_blank" class="btn btn-sm btn-outline-primary rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;"><i class="fab fa-linkedin-in"></i></a>
                                                <?php endif; ?>
                                                <?php if (!empty($member['instagram'])): ?>
                                                <a href="<?php echo $member['instagram']; ?>" target="_blank" class="btn btn-sm btn-outline-danger rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;"><i class="fab fa-instagram"></i></a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- اقتباس العضو -->
                                    <?php if (!empty($member['quote'])): ?>
                                    <div class="col-12 mt-2">
                                        <div class="p-2 bg-white rounded" style="border-right: 3px solid #007bff; text-align: right;">
                                            <blockquote class="blockquote mb-0" style="font-size: 0.9rem;">
                                                <p><i class="fas fa-quote-right me-2 text-primary"></i> <?php echo $member['quote']; ?></p>
                                            </blockquote>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- نبذة مختصرة -->
                            <div class="col-12 mb-3">
                                <div style="background-color: #f8f9fa; border-radius: 10px; padding: 15px;">
                                    <h5 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; display: inline-block; margin-bottom: 10px;">
                                        <i class="fas fa-user me-2"></i> نبذة مختصرة
                                    </h5>
                                    <div style="line-height: 1.6; text-align: right; padding: 5px; font-size: 0.95rem; max-height: 150px; overflow-y: auto;">
                                        <?php
                                        // تنظيم النص بإضافة مسافات بين الفقرات
                                        $bio = !empty($member['bio']) ? $member['bio'] : 'خبير في مجال الطاقة الشمسية.';
                                        // استبدال النقاط المتتالية بفواصل
                                        $bio = preg_replace('/\.{2,}/', '، ', $bio);
                                        // إضافة مسافة بعد كل نقطة إذا لم تكن موجودة
                                        $bio = preg_replace('/\.(?! )(?!$)/', '. ', $bio);
                                        // إضافة مسافة بعد كل فاصلة إذا لم تكن موجودة
                                        $bio = preg_replace('/،(?! )/', '، ', $bio);
                                        // تقسيم النص إلى فقرات عند وجود سطر جديد
                                        $paragraphs = explode("\n", $bio);
                                        // إعادة بناء النص مع تنسيق الفقرات
                                        foreach ($paragraphs as $paragraph) {
                                            if (trim($paragraph) !== '') {
                                                echo "<p style='margin-bottom: 8px;'>$paragraph</p>";
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <!-- المهارات والشهادات في صف واحد -->
                            <div class="col-12">
                                <div class="row">
                                    <!-- المهارات -->
                                    <?php if (!empty($skills)): ?>
                                    <div class="col-md-6 mb-3">
                                        <div style="background-color: #f8f9fa; border-radius: 10px; padding: 15px; height: 100%;">
                                            <h5 style="color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 5px; display: inline-block; margin-bottom: 10px;">
                                                <i class="fas fa-chart-bar me-2"></i> المهارات
                                            </h5>
                                            <div style="max-height: 150px; overflow-y: auto;">
                                                <?php foreach ($skills as $skill): ?>
                                                <div class="mb-2">
                                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                                        <span style="font-weight: 500; font-size: 0.9rem;"><?php echo $skill['skill_name']; ?></span>
                                                        <span class="badge bg-success" style="font-size: 0.8rem;"><?php echo $skill['skill_percentage']; ?>%</span>
                                                    </div>
                                                    <div class="progress" style="height: 8px; border-radius: 4px; background-color: #e9ecef;">
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $skill['skill_percentage']; ?>%; border-radius: 4px;" aria-valuenow="<?php echo $skill['skill_percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- الشهادات -->
                                    <?php if (!empty($certifications)): ?>
                                    <div class="col-md-<?php echo !empty($skills) ? '6' : '12'; ?> mb-3">
                                        <div style="background-color: #f8f9fa; border-radius: 10px; padding: 15px; height: 100%;">
                                            <h5 style="color: #fd7e14; border-bottom: 2px solid #fd7e14; padding-bottom: 5px; display: inline-block; margin-bottom: 10px;">
                                                <i class="fas fa-certificate me-2"></i> الشهادات
                                            </h5>
                                            <div style="max-height: 150px; overflow-y: auto;">
                                                <div class="row g-2">
                                                    <?php foreach ($certifications as $certification): ?>
                                                    <div class="col-12">
                                                        <div style="background-color: #fff4e6; border-radius: 6px; padding: 8px; border-right: 3px solid #fd7e14;">
                                                            <div style="display: flex; align-items: center;">
                                                                <i class="fas fa-award text-warning me-2" style="font-size: 1rem;"></i>
                                                                <div>
                                                                    <strong style="font-size: 0.9rem;"><?php echo $certification['certification_name']; ?></strong>
                                                                    <?php if (!empty($certification['certification_year'])): ?>
                                                                    <span class="text-muted ms-2" style="font-size: 0.8rem;"><?php echo $certification['certification_year']; ?></span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php elseif (!empty($member['certifications'])): ?>
                                    <div class="col-md-<?php echo !empty($skills) ? '6' : '12'; ?> mb-3">
                                        <div style="background-color: #f8f9fa; border-radius: 10px; padding: 15px; height: 100%;">
                                            <h5 style="color: #fd7e14; border-bottom: 2px solid #fd7e14; padding-bottom: 5px; display: inline-block; margin-bottom: 10px;">
                                                <i class="fas fa-certificate me-2"></i> الشهادات
                                            </h5>
                                            <div style="max-height: 150px; overflow-y: auto; font-size: 0.9rem;">
                                                <?php echo $member['certifications']; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تذييل النافذة المنبثقة -->
                    <div class="custom-modal-footer" style="padding: 10px; border-top: 1px solid #e9ecef; text-align: center; background-color: #f8f9fa;">
                        <button type="button" class="btn btn-primary" onclick="closeCustomModal('memberModal<?php echo $member['id']; ?>')" style="padding: 6px 20px; border-radius: 30px;">
                            <i class="fas fa-times me-1"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- قسم الإنجازات والمؤهلات - محسن -->
        <div class="team-achievements py-5 my-5">
            <div class="container">
                <div class="row justify-content-center mb-5">
                    <div class="col-lg-7 text-center">
                        <div class="section-title">
                            <span class="subtitle"><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['subtitle'] : 'إنجازاتنا'; ?></span>
                            <h2 class="title"><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['title'] : 'خبرة وكفاءة'; ?></h2>
                            <p class="section-description"><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['description'] : 'نفخر بإنجازاتنا وخبراتنا المتراكمة في مجال الطاقة الشمسية'; ?></p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <?php
                    $delay = 0;
                    foreach ($team_achievements as $achievement):
                    ?>
                    <div class="col-lg-4 col-md-6 mb-4 animate-on-scroll" <?php echo $delay > 0 ? 'data-delay="'.$delay.'"' : ''; ?>>
                        <div class="achievement-card">
                            <div class="achievement-icon">
                                <i class="<?php echo $achievement['icon']; ?>"></i>
                            </div>
                            <div class="achievement-content">
                                <div class="achievement-value"><?php echo $achievement['value']; ?></div>
                                <h4><?php echo $achievement['title']; ?></h4>
                                <p><?php echo $achievement['description']; ?></p>
                            </div>
                        </div>
                    </div>
                    <?php
                    $delay += 200;
                    endforeach;
                    ?>
                </div>

                <div class="row mt-5 align-items-center">
                    <div class="col-md-6 mb-4 mb-md-0 animate-on-scroll">
                        <div class="team-intro">
                            <h3><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['main_text'] : 'فريق متكامل من الخبراء'; ?></h3>
                            <p class="lead"><?php echo isset($section_settings['achievements']) ? $section_settings['achievements']['secondary_text'] : 'يتكون فريقنا من مهندسين متخصصين في تصميم أنظمة الطاقة الشمسية وفنيين محترفين في تركيب وصيانة هذه الأنظمة.'; ?></p>
                        </div>
                        <p><?php echo isset($section_settings['expertise_description']) ? $section_settings['expertise_description']['description'] : 'نحرص على تدريب فريقنا باستمرار على أحدث التقنيات والمعايير العالمية لضمان تقديم أفضل الخدمات لعملائنا. يمتلك فريقنا خبرة تزيد عن 10 سنوات في مجال الطاقة الشمسية.'; ?></p>

                        <div class="team-expertise mt-4">
                            <?php foreach ($team_expertise as $expertise): ?>
                            <div class="expertise-item">
                                <div class="d-flex justify-content-between">
                                    <span><?php echo $expertise['title']; ?></span>
                                    <span><?php echo $expertise['percentage']; ?>%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-<?php echo $expertise['color']; ?>" role="progressbar" style="width: <?php echo $expertise['percentage']; ?>%" aria-valuenow="<?php echo $expertise['percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="mt-4">
                            <a href="contact.php" class="btn btn-primary rounded-pill px-4">
                                <i class="fas fa-phone-alt me-2"></i>تواصل معنا
                            </a>
                            <a href="packages.php" class="btn btn-outline-primary rounded-pill px-4 ms-2">
                                <i class="fas fa-box me-2"></i>عرض البكجات
                            </a>
                            <a href="gallery.php" class="btn btn-outline-success rounded-pill px-4 ms-2">
                                <i class="fas fa-images me-2"></i>معرض الأعمال
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6 animate-on-scroll">
                        <img src="assets/img/team.jpg" alt="فريق العمل" class="img-fluid rounded-lg shadow-lg">
                    </div>
                </div>
            </div>
        </div>



        <!-- إضافة سكريبت لتفعيل النوافذ المنبثقة المخصصة والتصفية -->
        <script>
            // وظيفة لفتح النافذة المنبثقة المخصصة
            function openCustomModal(modalId) {
                // حفظ موضع التمرير الحالي
                const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

                // إظهار النافذة المنبثقة
                const modal = document.getElementById(modalId);
                if (modal) {
                    // تخزين موضع التمرير كسمة بيانات
                    modal.setAttribute('data-scroll-position', scrollPosition);

                    // إظهار النافذة المنبثقة
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // منع التمرير في الصفحة الرئيسية

                    // الحصول على مربع الحوار داخل النافذة المنبثقة
                    const modalDialog = modal.querySelector('.custom-modal-dialog');

                    // تعيين موضع النافذة المنبثقة بحيث تكون أسفل الشريط العلوي
                    if (modalDialog) {
                        // الحصول على ارتفاع الشريط العلوي
                        const navbarHeight = document.querySelector('.modern-navbar').offsetHeight || 70;

                        // تعيين هامش علوي ثابت يتجاوز الشريط العلوي
                        modalDialog.style.marginTop = (navbarHeight + 30) + 'px';

                        // التأكد من أن النافذة المنبثقة مرئية بالكامل
                        modal.scrollTop = 0;
                    }

                    // إضافة مستمع للنقر خارج النافذة المنبثقة لإغلاقها
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            closeCustomModal(modalId);
                        }
                    });

                    // إضافة مستمع للضغط على زر ESC لإغلاق النافذة المنبثقة
                    document.addEventListener('keydown', function(e) {
                        if (e.key === 'Escape') {
                            closeCustomModal(modalId);
                        }
                    });
                }
            }

            // وظيفة لإغلاق النافذة المنبثقة المخصصة
            function closeCustomModal(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    // إخفاء النافذة المنبثقة
                    modal.style.display = 'none';

                    // إعادة تمكين التمرير في الصفحة الرئيسية
                    document.body.style.overflow = '';

                    // استعادة موضع التمرير السابق
                    const scrollPosition = parseInt(modal.getAttribute('data-scroll-position') || '0');
                    setTimeout(function() {
                        window.scrollTo(0, scrollPosition);
                    }, 10);
                }
            }

            // لا حاجة لكود المعرض هنا - تم نقله إلى صفحة منفصلة
        </script>
    </div>
</section>

<!-- قسم الشهادات - محسن -->
<section class="certificates-section py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-7 text-center">
                <div class="section-title">
                    <span class="subtitle">اعتماداتنا</span>
                    <h2 class="title">شهاداتنا واعتماداتنا</h2>
                    <p class="section-description">نحن فخورون بحصولنا على شهادات واعتمادات من جهات محلية ودولية تؤكد جودة خدماتنا ومنتجاتنا</p>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10 mb-5 animate-on-scroll">
                <div class="certificate-description text-center mb-4">
                    <p>نفخر بحصولنا على العديد من الشهادات والاعتمادات من جهات محلية ودولية تؤكد التزامنا بأعلى معايير الجودة</p>
                </div>

                <!-- عرض الشهادات بشكل أفقي -->
                <div class="certificates-gallery">
                    <div class="row justify-content-center">
                        <?php if (empty($certificates)): ?>
                        <!-- رسالة في حالة عدم وجود شهادات -->
                        <div class="col-12 text-center">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle fa-2x mb-3"></i>
                                <h4>لا توجد شهادات حالياً</h4>
                                <p>سيتم إضافة الشهادات والاعتمادات قريباً.</p>
                            </div>
                        </div>
                        <?php else: ?>
                            <?php foreach ($certificates as $index => $certificate): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="certificate-card h-100 shadow-sm" style="border-radius: 10px; overflow: hidden; border: 1px solid #eee; transition: all 0.3s ease;">
                                    <div class="certificate-image text-center p-3" style="background-color: #f8f9fa;">
                                        <?php if (!empty($certificate['image']) && file_exists("uploads/certificates/" . $certificate['image'])): ?>
                                        <img src="uploads/certificates/<?php echo $certificate['image']; ?>" alt="<?php echo $certificate['title']; ?>" class="img-fluid" style="max-height: 180px; object-fit: contain;">
                                        <?php else: ?>
                                        <img src="assets/img/certificates-<?php echo ($index % 3) + 1; ?>.jpg" alt="<?php echo $certificate['title']; ?>" class="img-fluid" style="max-height: 180px; object-fit: contain;">
                                        <?php endif; ?>
                                    </div>
                                    <div class="certificate-title p-3">
                                        <h5 class="text-center mb-3" style="color: #007bff;"><?php echo $certificate['title']; ?></h5>
                                        <?php if (!empty($certificate['description'])): ?>
                                        <p class="certificate-desc" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; border-right: 3px solid #007bff; text-align: right; font-size: 0.95rem; line-height: 1.6; margin-bottom: 15px;">
                                            <?php echo mb_substr($certificate['description'], 0, 120) . (mb_strlen($certificate['description']) > 120 ? '...' : ''); ?>
                                        </p>
                                        <?php endif; ?>
                                        <div class="text-center">
                                            <button class="btn btn-primary btn-sm rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#certificateModal<?php echo $certificate['id']; ?>">
                                                <i class="fas fa-certificate me-1"></i> عرض التفاصيل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- نوافذ منبثقة لعرض الشهادات بحجم أكبر -->
        <?php if (!empty($certificates)): ?>
        <!-- نوافذ منبثقة للشهادات من قاعدة البيانات -->
        <?php foreach ($certificates as $certificate): ?>
        <div class="modal fade" id="certificateModal<?php echo $certificate['id']; ?>" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #007bff, #0056b3); color: white;">
                        <h5 class="modal-title" style="font-weight: bold;"><i class="fas fa-certificate me-2"></i> <?php echo $certificate['title']; ?></h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 text-center mb-3 mb-md-0">
                                <div style="background-color: #f8f9fa; border-radius: 10px; padding: 20px; height: 100%; display: flex; align-items: center; justify-content: center;">
                                    <?php if (!empty($certificate['image']) && file_exists("uploads/certificates/" . $certificate['image'])): ?>
                                    <img src="uploads/certificates/<?php echo $certificate['image']; ?>" alt="<?php echo $certificate['title']; ?>" class="img-fluid" style="max-height: 300px; object-fit: contain;">
                                    <?php else: ?>
                                    <img src="assets/img/certificates-<?php echo (array_search($certificate, $certificates) % 3) + 1; ?>.jpg" alt="<?php echo $certificate['title']; ?>" class="img-fluid" style="max-height: 300px; object-fit: contain;">
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if (!empty($certificate['description'])): ?>
                            <div class="col-md-6">
                                <div class="certificate-description" style="height: 100%;">
                                    <h5 class="mb-3" style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px; display: inline-block;">
                                        <i class="fas fa-info-circle me-2"></i> تفاصيل الشهادة
                                    </h5>
                                    <div style="background-color: #f8f9fa; border-radius: 10px; padding: 15px; border-right: 3px solid #007bff; text-align: right;">
                                        <p style="line-height: 1.8; font-size: 1.05rem; white-space: pre-line;"><?php echo nl2br($certificate['description']); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> لا يوجد وصف مفصل لهذه الشهادة.
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary rounded-pill px-4" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
        <?php endif; ?>

        <!-- قسم الأسئلة الشائعة -->
        <div class="row mt-5 justify-content-center">
            <div class="col-lg-8">
                <div class="faq-section">
                    <h3 class="text-center mb-4">الأسئلة الشائعة حول فريقنا</h3>
                    <div class="accordion" id="teamFAQ">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    ما هي خبرة فريقكم في مجال الطاقة الشمسية؟
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#teamFAQ">
                                <div class="accordion-body">
                                    يمتلك فريقنا خبرة تزيد عن 15 عاماً في مجال تصميم وتركيب وصيانة أنظمة الطاقة الشمسية. قمنا بتنفيذ أكثر من 500 مشروع ناجح في مختلف المحافظات، ويتكون فريقنا من مهندسين وفنيين متخصصين حاصلين على شهادات معتمدة في مجال الطاقة الشمسية.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    هل تقدمون خدمات ما بعد البيع والصيانة الدورية؟
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#teamFAQ">
                                <div class="accordion-body">
                                    نعم، نحن نقدم خدمات ما بعد البيع وخدمات الصيانة الدورية لجميع أنظمة الطاقة الشمسية التي نقوم بتركيبها. لدينا فريق متخصص في الصيانة والدعم الفني يقوم بزيارات دورية للتأكد من كفاءة النظام واستمراريته في العمل بأفضل أداء.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    هل تقدمون استشارات مجانية قبل تركيب النظام؟
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#teamFAQ">
                                <div class="accordion-body">
                                    نعم، نحن نقدم استشارات مجانية لعملائنا قبل تركيب النظام. يقوم فريقنا الهندسي بزيارة الموقع وتقييم احتياجاتكم وتقديم الحلول المناسبة لكم. كما نقوم بتقديم دراسة جدوى مبدئية توضح التكلفة المتوقعة والعائد على الاستثمار.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>
