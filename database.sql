-- قاعدة بيانات موقع شركة بريق ضوء الشمس
-- Solar Energy Company Database
-- تاريخ الإنشاء: 2024
-- الإصدار: 2.0

-- إن<PERSON>اء قاعدة البيانات (قم بتغيير الاسم حسب الحاجة)
CREATE DATABASE IF NOT EXISTS solar_energy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE solar_energy;

-- Create admins table
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    image VARCHAR(255) NOT NULL,
    category ENUM('panel', 'inverter', 'battery', 'accessories') NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create product_images table
CREATE TABLE IF NOT EXISTS product_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Create packages table
CREATE TABLE IF NOT EXISTS packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    product_ids JSON NOT NULL,
    image VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create media table
CREATE TABLE IF NOT EXISTS media (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('image', 'video') NOT NULL,
    file_url VARCHAR(255) NOT NULL,
    caption VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create company_info table
CREATE TABLE IF NOT EXISTS company_info (
    id INT PRIMARY KEY DEFAULT 1,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    map_embed_link TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (username: admin, password: admin123)
INSERT INTO admins (username, password) VALUES ('admin', '$2y$10$8tGmGEOt/Kg0LdLBFY1t3.qlP.eL7o5Z2RgN9JE7VVDtUfep0PrIG');

-- Create contact_page table
CREATE TABLE IF NOT EXISTS contact_page (
    id INT PRIMARY KEY DEFAULT 1,
    page_title VARCHAR(100) NOT NULL DEFAULT 'تواصل معنا',
    page_subtitle VARCHAR(255) NOT NULL DEFAULT 'نحن هنا للإجابة على استفساراتك',
    form_title VARCHAR(100) NOT NULL DEFAULT 'أرسل لنا رسالة',
    form_subtitle TEXT NOT NULL DEFAULT 'نحن نقدر تواصلك معنا ونسعى دائماً لتقديم أفضل الخدمات لعملائنا',
    map_title VARCHAR(100) NOT NULL DEFAULT 'موقعنا على الخريطة',
    faq_title VARCHAR(100) NOT NULL DEFAULT 'الأسئلة الشائعة',
    faq_subtitle TEXT NOT NULL DEFAULT 'إجابات على الأسئلة الأكثر شيوعاً حول أنظمة الطاقة الشمسية وخدماتنا',
    privacy_text TEXT NOT NULL DEFAULT 'نحن نحترم خصوصيتك ولن نشارك معلوماتك مع أي طرف ثالث.',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create faqs table
CREATE TABLE IF NOT EXISTS faqs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question VARCHAR(255) NOT NULL,
    answer TEXT NOT NULL,
    is_expanded TINYINT(1) DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default contact page data
INSERT INTO contact_page (page_title, page_subtitle, form_title, form_subtitle, map_title, faq_title, faq_subtitle, privacy_text)
VALUES (
    'تواصل معنا',
    'نحن هنا للإجابة على استفساراتك',
    'أرسل لنا رسالة',
    'نحن نقدر تواصلك معنا ونسعى دائماً لتقديم أفضل الخدمات لعملائنا',
    'موقعنا على الخريطة',
    'الأسئلة الشائعة',
    'إجابات على الأسئلة الأكثر شيوعاً حول أنظمة الطاقة الشمسية وخدماتنا',
    'نحن نحترم خصوصيتك ولن نشارك معلوماتك مع أي طرف ثالث.'
);

-- Insert default FAQs
INSERT INTO faqs (question, answer, is_expanded, sort_order) VALUES
('ما هي مدة تركيب نظام الطاقة الشمسية؟', '<p>تختلف مدة التركيب حسب حجم النظام، ولكن عادة ما تستغرق من 1-3 أيام للأنظمة المنزلية.</p><p class="mb-0 text-muted small">يقوم فريقنا المتخصص بتقييم المشروع وتحديد المدة الزمنية المتوقعة للتركيب قبل البدء بالعمل.</p>', 1, 1),
('هل يمكن تركيب النظام في أي مكان؟', '<p>نعم، يمكن تركيب النظام في أي مكان يتوفر فيه مساحة كافية وتعرض جيد لأشعة الشمس. يمكن تركيبه على الأسطح أو في الحدائق أو في أي مكان مناسب.</p><p class="mb-0 text-muted small">يقوم فريقنا بزيارة الموقع وتقييم أفضل مكان للتركيب لضمان الحصول على أقصى استفادة من النظام.</p>', 0, 2),
('ما هي مدة الضمان؟', '<p>تختلف مدة الضمان حسب المكونات:</p><ul><li>الألواح الشمسية: 25 سنة</li><li>الإنفرتر: 5 سنوات</li><li>البطاريات: 2-5 سنوات حسب النوع</li></ul><p class="mb-0 text-muted small">نقدم أيضاً خدمة الصيانة الدورية لضمان استمرارية عمل النظام بكفاءة عالية.</p>', 0, 3),
('كم يوفر نظام الطاقة الشمسية من فاتورة الكهرباء؟', '<p>يمكن لنظام الطاقة الشمسية أن يوفر ما يصل إلى 90% من فاتورة الكهرباء، اعتماداً على حجم النظام واستهلاك الطاقة.</p><p class="mb-0 text-muted small">يمكننا تقديم دراسة جدوى مفصلة لتقدير التوفير المتوقع بناءً على احتياجاتك الخاصة.</p>', 0, 4);

-- Insert default company info
INSERT INTO company_info (phone, email, address, map_embed_link)
VALUES ('+964 XXX XXX XXXX', '<EMAIL>', 'Baghdad, Iraq', '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3333.9598754880313!2d44.366!3d33.315!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzPCsDE4JzU0LjAiTiA0NMKwMjEnNTcuNiJF!5e0!3m2!1sen!2sus!4v1620000000000!5m2!1sen!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>');
