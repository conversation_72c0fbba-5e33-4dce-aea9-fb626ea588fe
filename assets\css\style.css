/* ملف CSS المخصص لموقع ضوء الشمس */

/* الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* متغيرات الألوان - الوضع النهاري (الافتراضي) */
:root {
    /* اللون الأساسي البرتقالي الذهبي */
    --primary-color: #c86808;
    --primary-light: #e87a09;
    --primary-dark: #a85607;
    --primary-gradient: linear-gradient(45deg, #c86808, #e87a09);
    --primary-transparent: rgba(200, 104, 8, 0.1);

    /* ألوان الخلفيات والنصوص */
    --body-bg: #f8f9fa;
    --text-color: #212529;
    --text-muted: #6c757d;
    --text-light: #f8f9fa;

    /* ألوان البطاقات والحدود */
    --card-bg: #ffffff;
    --card-border: rgba(0, 0, 0, 0.125);
    --card-shadow: 0 10px 20px rgba(200, 104, 8, 0.08);
    --card-shadow-hover: 0 15px 30px rgba(200, 104, 8, 0.15);

    /* ألوان شريط التنقل */
    --navbar-bg: #ffffff;
    --navbar-color: rgba(0, 0, 0, 0.7);
    --navbar-active: #c86808;
    --navbar-hover-bg: rgba(200, 104, 8, 0.05);

    /* ألوان التذييل */
    --footer-bg: #343a40;
    --footer-color: #ffffff;

    /* ألوان النماذج والجداول */
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --input-focus-border: #c86808;
    --input-focus-shadow: 0 0 0 0.25rem rgba(200, 104, 8, 0.25);
    --table-border: #dee2e6;
    --table-stripe: rgba(200, 104, 8, 0.03);
    --table-hover: rgba(200, 104, 8, 0.05);

    /* ألوان القوائم المنسدلة والنوافذ المنبثقة */
    --modal-bg: #ffffff;
    --dropdown-bg: #ffffff;
    --dropdown-hover: rgba(200, 104, 8, 0.05);

    /* ألوان التنبيهات */
    --alert-success-bg: #d1e7dd;
    --alert-danger-bg: #f8d7da;
    --alert-info-bg: #cff4fc;
    --alert-warning-bg: #fff3cd;

    /* ألوان الأزرار */
    --btn-primary-bg: #c86808;
    --btn-primary-border: #c86808;
    --btn-primary-hover: #a85607;
    --btn-primary-active: #8f4906;

    /* ألوان إضافية */
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    /* ألوان الظلال والتأثيرات */
    --shadow-color: rgba(200, 104, 8, 0.15);
    --shadow-sm: 0 2px 5px rgba(200, 104, 8, 0.1);
    --shadow-md: 0 5px 15px rgba(200, 104, 8, 0.15);
    --shadow-lg: 0 10px 25px rgba(200, 104, 8, 0.2);

    /* ألوان لوحة الإدارة */
    --admin-sidebar-bg: #343a40;
    --admin-sidebar-color: #ffffff;
    --admin-sidebar-active: #c86808;
}

/* متغيرات الألوان - الوضع الليلي */
[data-theme="dark"] {
    /* اللون الأساسي البرتقالي الذهبي - نسخة الوضع الليلي */
    --primary-color: #e87a09;
    --primary-light: #f89c3c;
    --primary-dark: #a85607;
    --primary-gradient: linear-gradient(45deg, #c86808, #f89c3c);
    --primary-transparent: rgba(232, 122, 9, 0.15);

    /* ألوان الخلفيات والنصوص */
    --body-bg: #121212;
    --text-color: #e0e0e0;
    --text-muted: #a0a0a0;
    --text-light: #f8f9fa;

    /* تأكيد ظهور صور السلايدر في الوضع الليلي */
    --slider-overlay-color: rgba(0, 0, 0, 0.6);

    /* ألوان البطاقات والحدود */
    --card-bg: #1e1e1e;
    --card-border: rgba(255, 255, 255, 0.125);
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    --card-shadow-hover: 0 15px 30px rgba(232, 122, 9, 0.2);

    /* ألوان شريط التنقل */
    --navbar-bg: #1e1e1e;
    --navbar-color: rgba(255, 255, 255, 0.75);
    --navbar-active: #e87a09;
    --navbar-hover-bg: rgba(232, 122, 9, 0.15);

    /* ألوان التذييل */
    --footer-bg: #1e1e1e;
    --footer-color: #e0e0e0;

    /* ألوان النماذج والجداول */
    --input-bg: #2d2d2d;
    --input-border: #444444;
    --input-focus-border: #e87a09;
    --input-focus-shadow: 0 0 0 0.25rem rgba(232, 122, 9, 0.25);
    --table-border: #444444;
    --table-stripe: rgba(232, 122, 9, 0.05);
    --table-hover: rgba(232, 122, 9, 0.1);

    /* ألوان القوائم المنسدلة والنوافذ المنبثقة */
    --modal-bg: #1e1e1e;
    --dropdown-bg: #2d2d2d;
    --dropdown-hover: #3d3d3d;

    /* ألوان التنبيهات */
    --alert-success-bg: #0d392d;
    --alert-danger-bg: #3d1c1e;
    --alert-info-bg: #0d3c47;
    --alert-warning-bg: #3d2e0d;

    /* ألوان الأزرار */
    --btn-primary-bg: #e87a09;
    --btn-primary-border: #e87a09;
    --btn-primary-hover: #c86808;
    --btn-primary-active: #a85607;

    /* ألوان إضافية */
    --secondary-color: #8c8c8c;
    --success-color: #2fb85a;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #e0e0e0;
    --dark-color: #1e1e1e;

    /* ألوان الظلال والتأثيرات */
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);

    /* ألوان لوحة الإدارة */
    --admin-sidebar-bg: #1e1e1e;
    --admin-sidebar-color: #e0e0e0;
    --admin-sidebar-active: #e87a09;
}

/* تطبيق المتغيرات */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* شريط التنقل المحسن */
.navbar {
    background-color: var(--navbar-bg) !important;
    transition: all 0.3s ease;
    padding: 15px 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* تحسين شريط التنقل في الوضع الليلي */
[data-theme="dark"] .navbar {
    background-color: var(--navbar-bg) !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* تحسين القائمة المنسدلة في الوضع الليلي */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color) !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: var(--primary-color) !important;
    color: #fff !important;
}

[data-theme="dark"] .dropdown-item.active {
    background-color: var(--primary-color) !important;
    color: #fff !important;
}

/* تحسين النوافذ المنبثقة في الوضع الليلي */
[data-theme="dark"] .modal-content {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
}

[data-theme="dark"] .modal-header {
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--card-border) !important;
}

[data-theme="dark"] .modal-body {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .modal-caption {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* تحسين زر الإغلاق في النوافذ المنبثقة */
.modal .btn-close {
    z-index: 1060;
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50% !important;
    opacity: 1 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.modal .btn-close:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.modal .btn-close:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
}

/* تحسين النافذة المنبثقة للمعرض */
#galleryModal .modal-dialog {
    max-width: 95vw;
    margin: 1rem auto;
}

#galleryModal .modal-content {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

#galleryModal .modal-body {
    padding: 0 !important;
}

#galleryModal img {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* تحسين النافذة المنبثقة في الوضع الليلي */
[data-theme="dark"] #galleryModal .btn-close {
    background-color: rgba(255, 255, 255, 0.9) !important;
}

[data-theme="dark"] #galleryModal #galleryModalCaption {
    background-color: rgba(0, 0, 0, 0.8) !important;
    color: #fff !important;
}

/* أنماط صفحة المعرض */

/* إحصائيات المعرض */
.stats-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 24px;
}

.stats-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stats-content p {
    color: #666;
    font-weight: 500;
    margin: 0;
}

/* أزرار التصفية */
.gallery-filters {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 20px;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.filter-btn {
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* شبكة المعرض */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.gallery-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.gallery-item.show {
    opacity: 1;
    transform: translateY(0);
}

.gallery-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-image,
.gallery-video {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-image img,
.gallery-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-image img,
.gallery-card:hover .gallery-video video {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.9), rgba(0, 86, 179, 0.9));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.gallery-action-btn {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-action-btn:hover {
    background: white;
    color: var(--primary-color);
    transform: scale(1.1);
}

.gallery-info {
    text-align: center;
}

.gallery-type {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.gallery-card-content {
    padding: 20px;
}

.gallery-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
    line-height: 1.4;
}

.gallery-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
}

.gallery-date,
.gallery-category {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* النافذة المنبثقة للوسائط */
.media-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    cursor: pointer;
}

.lightbox-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.lightbox-close {
    position: absolute;
    top: 30px;
    right: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-nav:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-prev {
    left: 30px;
}

.lightbox-next {
    right: 30px;
}

.lightbox-content {
    max-width: 90%;
    max-height: 90%;
    text-align: center;
}

.lightbox-media {
    margin-bottom: 20px;
}

.lightbox-media img,
.lightbox-media video {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 10px;
}

.lightbox-info h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.lightbox-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* رسالة عدم وجود محتوى */
.no-content-message {
    text-align: center;
    padding: 80px 20px;
    background: #f8f9fa;
    border-radius: 20px;
}

.no-content-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.no-content-message h3 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.no-content-message p {
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* قسم دعوة للعمل */
.cta-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    margin: 60px 0;
}

.cta-section h2 {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.cta-section .lead {
    color: #666;
    margin-bottom: 0;
}

/* تحسينات الوضع الليلي للمعرض */

[data-theme="dark"] .stats-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
}

[data-theme="dark"] .stats-content h3 {
    color: var(--primary-color);
}

[data-theme="dark"] .stats-content p {
    color: #aaa;
}

[data-theme="dark"] .gallery-filters {
    background: var(--card-bg);
}

[data-theme="dark"] .filter-btn {
    background: var(--card-bg);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-theme="dark"] .gallery-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
}

[data-theme="dark"] .gallery-title {
    color: var(--text-color);
}

[data-theme="dark"] .gallery-meta {
    color: #aaa;
}

[data-theme="dark"] .no-content-message {
    background: var(--card-bg);
}

[data-theme="dark"] .no-content-message h3 {
    color: var(--text-color);
}

[data-theme="dark"] .no-content-message p {
    color: #aaa;
}

[data-theme="dark"] .cta-section {
    background: linear-gradient(135deg, var(--card-bg), #1a1a1a);
}

[data-theme="dark"] .cta-section h2 {
    color: var(--text-color);
}

[data-theme="dark"] .cta-section .lead {
    color: #aaa;
}

/* إصلاح السلايدر للوضع الليلي والنهاري */
.hero-banner,
.modern-banner {
    transition: none !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    position: relative !important;
}

/* تحسين السلايدر في الوضع الليلي - بدون تداخل مع الصور */
[data-theme="dark"] .hero-banner .container,
[data-theme="dark"] .modern-banner .container {
    position: relative;
    z-index: 2;
}

.modern-navbar {
    border-bottom: 3px solid var(--primary-color);
}

.navbar.scrolled {
    padding: 10px 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.navbar-brand {
    font-weight: 700;
    color: var(--text-color) !important;
    font-size: 1.6rem;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.brand-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    font-size: 1.8rem;
    font-weight: 700;
}

.brand-secondary {
    color: var(--secondary-color);
    font-size: 0.9rem;
    font-weight: 400;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--navbar-color);
    font-weight: 500;
    padding: 0.7rem 1rem;
    margin: 0 0.2rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-icon {
    margin-left: 8px;
    font-size: 1.1rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--navbar-active);
    background-color: var(--navbar-hover-bg);
    transform: translateY(-2px);
}

.navbar-light .navbar-nav .nav-link:hover .nav-icon {
    transform: scale(1.2);
    color: var(--navbar-active);
}

.navbar-light .navbar-nav .nav-link.active {
    color: var(--navbar-active);
    background-color: var(--primary-transparent);
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.navbar-light .navbar-nav .nav-link.active .nav-icon {
    color: var(--navbar-active);
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background-color: rgba(13, 110, 253, 0.05);
    border-radius: 8px;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

.navbar-toggler:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.navbar-toggler-icon {
    width: 1.5em;
    height: 1.5em;
}

/* البانر الرئيسي المحسن */
.hero-banner {
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../img/solar-banner.jpg');
    background-size: cover;
    background-position: center;
    color: white !important;
    padding: 120px 0;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px var(--shadow-color);
    height: 500px; /* ارتفاع ثابت للسلايدر */
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-banner {
    padding: 0;
    position: relative;
    background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('../img/solar-banner.jpg');
    background-size: cover;
    background-position: center;
    height: 500px; /* ارتفاع ثابت للسلايدر */
    display: flex;
    align-items: center;
    justify-content: center;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(13, 110, 253, 0.3), transparent 70%);
    z-index: 1;
}

.hero-banner .container {
    position: relative;
    z-index: 2;
}

.hero-banner .row {
    position: relative;
    z-index: 3;
}

.animated-heading {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: fadeInDown 1s ease-out;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.animated-text {
    font-size: 1.4rem;
    margin-bottom: 30px;
    color: white !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    animation: fadeInDown 1s ease-out 0.3s;
    animation-fill-mode: both;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.banner-buttons {
    animation: fadeInUp 1s ease-out 0.6s;
    animation-fill-mode: both;
    margin-top: 30px;
}

.animated-btn {
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    background: var(--primary-gradient);
    border: none;
    color: white;
}

.animated-btn-delay {
    animation-delay: 0.3s;
}

.animated-btn:hover, .animated-btn-delay:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
}

.banner-image-container {
    position: relative;
    animation: floatImage 6s ease-in-out infinite;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.3));
}

.banner-image {
    animation: fadeInLeft 1s ease-out;
    max-height: 400px;
}

@keyframes floatImage {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* تأثيرات الحركة عند التمرير */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* قسم من نحن المحسن */
.about-section {
    position: relative;
    padding: 80px 0;
}

.section-title {
    position: relative;
    margin-bottom: 30px;
}

.section-title .subtitle {
    display: inline-block;
    font-size: 1rem;
    font-weight: 500;
    color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
    padding: 5px 15px;
    border-radius: 50px;
    margin-bottom: 10px;
}

.section-title .title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-title .title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

.section-description {
    font-size: 1.1rem;
    color: var(--secondary-color);
    max-width: 700px;
    margin: 0 auto;
}

.about-image-wrapper {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.about-image {
    width: 100%;
    height: auto;
    transition: transform 0.5s ease;
}

.about-image-wrapper:hover .about-image {
    transform: scale(1.05);
}

.experience-badge {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

.experience-badge .years {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.8rem;
    text-align: center;
}

.about-feature {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.about-feature:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateY(-5px);
}

.feature-icon-sm {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.about-feature h5 {
    font-weight: 600;
    margin-bottom: 5px;
}

.about-feature p {
    color: var(--secondary-color);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* قسم المميزات المحسن */
.features-section {
    background-color: var(--light-color);
    position: relative;
    padding: 80px 0;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    opacity: 0.05;
    border-radius: 50%;
}

.features-section::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--success-color), var(--primary-color));
    opacity: 0.05;
    border-radius: 50%;
}

.feature-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    padding: 30px;
    border-bottom: 3px solid transparent;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid var(--primary-color);
}

.feature-card.active {
    border-bottom: 3px solid var(--primary-color);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon-wrapper {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon-wrapper {
    transform: scale(1.1) rotate(10deg);
}

.feature-content h4 {
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--text-color);
}

.feature-content p {
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.feature-stats {
    display: flex;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* بطاقات المنتجات المحسنة */
.product-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 100%;
    background-color: var(--card-bg);
    border: none;
    position: relative;
    border-bottom: 3px solid transparent;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-hover);
    border-bottom: 3px solid var(--primary-color);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 15px 0 0 15px;
}

.product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.product-card .card-img-top {
    height: 280px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-card .card-body {
    padding: 1.5rem;
}

.product-card .card-title {
    font-weight: 700;
    color: var(--text-color);
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    position: relative;
    padding-bottom: 10px;
}

.product-card .card-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 2px;
}

.product-card .card-text {
    color: var(--text-color);
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.product-features {
    list-style: none;
    padding: 0;
    margin-bottom: 1.5rem;
}

.product-features li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.product-features li i {
    color: var(--success-color);
    margin-left: 10px;
    font-size: 0.9rem;
}

.product-price {
    margin: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* مواصفات المنتج */
.product-specs {
    margin: 15px 0;
}

.product-specs h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.product-card .price {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--success-color);
    display: block;
}

.product-card .btn {
    border-radius: 50px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-border);
    color: white;
}

.product-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background-color: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
}

.product-card .carousel {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card .carousel-control-prev,
.product-card .carousel-control-next {
    width: 10%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .carousel-control-prev,
.product-card:hover .carousel-control-next {
    opacity: 0.8;
}

/* جدول المواصفات */
.specs-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.specs-table thead {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
}

.specs-table th, .specs-table td {
    padding: 15px;
    vertical-align: middle;
}

.specs-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.specs-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تحسينات النافذة المنبثقة */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 700;
}

.modal-body {
    padding: 25px;
}

.modal-body h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.modal-body h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--success-color));
    border-radius: 3px;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* قسم الاتصال */
.contact-section {
    position: relative;
    overflow: hidden;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    opacity: 0.05;
    border-radius: 50%;
}

/* بطاقات البكجات */
.package-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin-bottom: 30px;
    background-color: var(--card-bg);
    border: none;
    position: relative;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px var(--shadow-color);
}

.package-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--success-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.package-card:hover::before {
    opacity: 1;
}

.package-card .card-title {
    font-weight: 700;
    color: var(--text-color);
    font-size: 1.4rem;
    margin-bottom: 0.75rem;
}

.package-card .card-text {
    color: var(--text-color);
    margin-bottom: 1rem;
    opacity: 0.8;
}

.package-card .price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--success-color);
    display: block;
    margin-bottom: 1.5rem;
}

.package-card .btn {
    border-radius: 50px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.package-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* معرض الصور - محسن */
.gallery-section {
    position: relative;
    background-color: var(--body-bg);
    overflow: hidden;
}

.gallery-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.gallery-container {
    position: relative;
    z-index: 1;
}

.gallery-item {
    margin-bottom: 30px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    background-color: var(--card-bg);
    border: none;
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px var(--shadow-color);
}

.gallery-item-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.gallery-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(13, 110, 253, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-popup {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-popup {
    transform: scale(1);
}

.video-wrapper {
    position: relative;
    height: 100%;
}

.video-wrapper video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.video-play-button:hover {
    background-color: white;
    transform: translate(-50%, -50%) scale(1.1);
}

.gallery-caption {
    padding: 20px;
    background-color: var(--card-bg);
    color: var(--text-color);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.gallery-caption h5 {
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--text-color);
    font-size: 1.1rem;
}

.gallery-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: var(--secondary-color);
    margin-top: 10px;
}

.gallery-meta i {
    margin-left: 5px;
    color: var(--primary-color);
}

.modal-caption {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* تحسينات صفحات العناوين */
.page-header {
    position: relative;
    background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)), url('../img/team-banner.jpg');
    background-size: cover;
    background-position: center;
    padding: 100px 0 80px;
    color: white;
    margin-bottom: 0;
    overflow: hidden;
}

/* بانر صفحة الألواح الشمسية */
.panels-header {
    background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('../img/panels-banner.jpg');
}

/* بانر صفحة الإنفرترات */
.inverters-header {
    background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('../img/inverters-banner.jpg');
}

/* بانر صفحة البطاريات */
.batteries-header {
    background-image: linear-gradient(120deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('../img/batteries-banner.jpg');
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-header-shape {
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: var(--body-bg);
    clip-path: polygon(0 100%, 100% 100%, 100% 0);
}

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-top: 15px;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

.team-section {
    position: relative;
    padding: 80px 0;
    background-color: var(--body-bg);
}

.team-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(to bottom right, var(--primary-color), var(--success-color));
    opacity: 0.05;
    z-index: 0;
}

.team-section .container {
    position: relative;
    z-index: 1;
}

.team-intro {
    margin-bottom: 30px;
    position: relative;
}

.team-intro h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.team-intro h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--success-color));
    border-radius: 3px;
}

.team-intro .lead {
    font-size: 1.2rem;
    color: var(--text-color);
    opacity: 0.9;
    margin-bottom: 20px;
}

/* قسم الإنجازات */
.team-achievements {
    position: relative;
    background-color: var(--light-color);
    overflow: hidden;
}

.team-achievements::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.team-achievements::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--success-color), var(--primary-color));
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.achievement-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    padding: 30px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.achievement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.achievement-card:hover .achievement-icon {
    transform: scale(1.1) rotate(10deg);
}

.achievement-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    line-height: 1;
}

.achievement-content h4 {
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--text-color);
}

.achievement-content p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

.team-expertise {
    margin-top: 30px;
}

.expertise-item {
    margin-bottom: 20px;
}

.expertise-item span {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.progress {
    height: 8px;
    margin-top: 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.progress-bar {
    border-radius: 4px;
    transition: width 1.5s ease;
}

/* بطاقات أعضاء الفريق - محسنة */
.team-member-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.team-member-card.enhanced {
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

.team-member-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.member-image {
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member-card:hover .member-image img {
    transform: scale(1.05);
}

/* تحسينات جديدة */
.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(13, 110, 253, 0.9), rgba(0, 0, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    padding: 20px;
}

.team-member-card:hover .member-overlay {
    opacity: 1;
}

.member-details {
    color: white;
    text-align: center;
    transform: translateY(20px);
    transition: all 0.4s ease 0.1s;
    opacity: 0;
    width: 100%;
}

.team-member-card:hover .member-details {
    transform: translateY(0);
    opacity: 1;
}

.member-details p {
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.member-skills {
    margin-top: 15px;
}

.skill-item {
    margin-bottom: 10px;
}

.skill-label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.85rem;
    text-align: right;
}

.skill-bar {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(to right, #ffffff, #f8f9fa);
    border-radius: 3px;
}

.social-icons {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    padding: 20px 15px 15px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    z-index: 2;
}

.team-member-card:hover .social-icons {
    opacity: 1;
    transform: translateY(0);
}

.social-icons a {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.member-info {
    padding: 25px;
    text-align: center;
    position: relative;
}

.member-badge {
    position: absolute;
    top: -15px;
    right: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    font-size: 0.8rem;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.member-info h4 {
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--text-color);
    font-size: 1.3rem;
}

.member-info .position {
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.member-quote {
    font-style: italic;
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 15px;
    position: relative;
    padding: 0 10px;
}

.member-quote::before,
.member-quote::after {
    content: '"';
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: bold;
}

/* تحسينات النافذة المنبثقة للسيرة الذاتية */
.modal-content {
    overflow: hidden;
}

.gallery-filter {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.gallery-filter .btn {
    margin: 5px;
    padding: 8px 20px;
    border-radius: 50px;
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--card-border);
    transition: all 0.3s ease;
}

.gallery-filter .btn.active, .gallery-filter .btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* قسم الشهادات */
.certificates-section {
    position: relative;
    background-color: var(--body-bg);
    overflow: hidden;
}

.certificates-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.full-certificate-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.full-certificate-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.certificate-header {
    padding: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.certificate-icon-large {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.certificate-header h3 {
    font-weight: 700;
    margin-bottom: 0;
    font-size: 1.5rem;
}

.full-certificate-image {
    position: relative;
    overflow: hidden;
}

.full-certificate-image img {
    width: 100%;
    display: block;
    transition: transform 0.5s ease;
}

.full-certificate-card:hover .full-certificate-image img {
    transform: scale(1.02);
}

.certificate-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(13, 110, 253, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.full-certificate-card:hover .certificate-overlay {
    opacity: 1;
}

.certificate-zoom {
    padding: 10px 20px;
    border-radius: 30px;
    background-color: white;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.certificate-zoom i {
    margin-left: 8px;
}

.full-certificate-card:hover .certificate-zoom {
    transform: scale(1);
}

.certificate-zoom:hover {
    background-color: var(--primary-color);
    color: white;
}

.certificate-content {
    padding: 25px;
}

.certificate-description {
    color: var(--secondary-color);
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 0;
}

/* قسم الأسئلة الشائعة */
.faq-section {
    margin-top: 50px;
}

.accordion-item {
    border: none;
    margin-bottom: 15px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.accordion-button {
    font-weight: 600;
    padding: 20px;
    background-color: var(--card-bg);
    color: var(--text-color);
}

.accordion-button:not(.collapsed) {
    color: var(--primary-color);
    background-color: var(--card-bg);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 0, 0, 0.1);
}

.accordion-button::after {
    background-size: 1.2rem;
    transition: all 0.3s ease;
}

.accordion-body {
    padding: 20px;
    background-color: var(--card-bg);
    color: var(--secondary-color);
    font-size: 0.95rem;
    line-height: 1.6;
}

/* زر واتساب الثابت */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.whatsapp-float .btn {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px var(--shadow-color);
}

/* زر العودة للأعلى */
#back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    box-shadow: 0 4px 10px var(--shadow-color);
    background-color: var(--primary-color);
    color: white;
}

/* صفحة الاتصال */
.contact-info {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px var(--shadow-color);
    margin-bottom: 30px;
    color: var(--text-color);
    border: 1px solid var(--card-border);
}

.contact-info i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .hero-banner {
        padding: 60px 0;
    }

    .hero-banner h1 {
        font-size: 2rem;
    }

    .hero-banner p {
        font-size: 1rem;
    }
}

/* لوحة الإدارة */
.admin-sidebar {
    background-color: var(--admin-sidebar-bg);
    color: var(--admin-sidebar-color);
    min-height: 100vh;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.admin-sidebar .nav-link {
    color: var(--admin-sidebar-color);
    padding: 10px 15px;
    margin-bottom: 5px;
    border-radius: 5px;
    opacity: 0.8;
    transition: background-color 0.3s ease, opacity 0.3s ease;
}

.admin-sidebar .nav-link:hover, .admin-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 1;
}

.admin-sidebar .nav-link i {
    margin-right: 10px;
}

.admin-content {
    padding: 20px;
    background-color: var(--body-bg);
    transition: background-color 0.3s ease;
}

.admin-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow-color);
    margin-bottom: 20px;
    background-color: var(--card-bg);
    border-color: var(--card-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.admin-card .card-header {
    background-color: var(--card-bg);
    font-weight: 700;
    color: var(--text-color);
    border-bottom: 1px solid var(--card-border);
}

/* تخصيص العناصر الأخرى */
.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    color: var(--text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* تحسين ظهور النصوص في قسم مميزات الطاقة الشمسية */
.card h4, .card h5 {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.card p {
    color: var(--text-color);
}

/* تحسين ظهور النصوص في جميع الأقسام */
p, span, div {
    color: inherit;
}

/* تحسينات شاملة للوضع الليلي */
[data-theme="dark"] {
    color-scheme: dark;
}

/* تحسينات الخلفيات والنصوص في الوضع الليلي */
[data-theme="dark"] body,
[data-theme="dark"] main,
[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid,
[data-theme="dark"] .row,
[data-theme="dark"] .col,
[data-theme="dark"] div {
    background-color: var(--body-bg);
    color: var(--text-color);
}

[data-theme="dark"] .bg-white,
[data-theme="dark"] .bg-light {
    background-color: var(--dark-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .bg-light * {
    color: var(--text-color);
}

[data-theme="dark"] .card,
[data-theme="dark"] .list-group-item {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    color: var(--text-color);
}

[data-theme="dark"] .modal-content {
    background-color: var(--modal-bg);
    color: var(--text-color);
}

[data-theme="dark"] .text-dark {
    color: var(--text-color) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

/* تحسينات العناوين والنصوص في الوضع الليلي */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] .lead,
[data-theme="dark"] .display-1,
[data-theme="dark"] .display-2,
[data-theme="dark"] .display-3,
[data-theme="dark"] .display-4 {
    color: var(--text-color) !important;
}

/* تحسينات شريط التنقل في الوضع الليلي */
[data-theme="dark"] .navbar,
[data-theme="dark"] .navbar-light {
    background-color: var(--navbar-bg) !important;
}

[data-theme="dark"] .navbar-light .navbar-brand {
    color: var(--text-color) !important;
}

[data-theme="dark"] .navbar-light .navbar-nav .nav-link {
    color: var(--navbar-color);
}

[data-theme="dark"] .navbar-light .navbar-toggler-icon {
    filter: invert(1);
}

/* تحسين ظهور أيقونة تبديل الوضع في شريط التنقل */
.navbar-theme-toggle {
    background-color: transparent !important;
    border: none !important;
    color: var(--navbar-color) !important;
}

[data-theme="dark"] .navbar-theme-toggle {
    color: var(--text-color) !important;
}

/* تحسينات الروابط في الوضع الليلي */
[data-theme="dark"] a:not(.btn):not(.nav-link) {
    color: var(--primary-light);
}

[data-theme="dark"] a:not(.btn):not(.nav-link):hover {
    color: var(--primary-color);
}

[data-theme="dark"] footer a.text-white,
[data-theme="dark"] footer a {
    color: var(--footer-color) !important;
}

/* تحسينات الجداول في الوضع الليلي */
[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075);
}

/* تحسينات النماذج في الوضع الليلي */
[data-theme="dark"] .form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

/* تحسينات الأقسام المختلفة في الوضع الليلي */
[data-theme="dark"] .team-section,
[data-theme="dark"] .about-section,
[data-theme="dark"] .features-section,
[data-theme="dark"] .gallery-section,
[data-theme="dark"] .contact-section,
[data-theme="dark"] .certificates-section,
[data-theme="dark"] .faq-section {
    background-color: var(--body-bg);
}

[data-theme="dark"] .team-section p,
[data-theme="dark"] .gallery-item .caption p,
[data-theme="dark"] .gallery-item .caption h5,
[data-theme="dark"] section p,
[data-theme="dark"] .container p {
    color: var(--text-color);
}

/* تحسينات الأزرار في الوضع الليلي */
[data-theme="dark"] .btn-light {
    background-color: var(--dark-color);
    border-color: var(--card-border);
    color: var(--text-color);
}

[data-theme="dark"] .btn-light:hover {
    background-color: var(--card-bg);
    color: var(--primary-color);
}

[data-theme="dark"] .btn-primary {
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-border);
    color: white;
}

[data-theme="dark"] .btn-primary:hover {
    background-color: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    color: white;
}

[data-theme="dark"] .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* تحسينات القوائم المنسدلة في الوضع الليلي */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--dropdown-hover);
    color: var(--primary-color);
}

/* أيقونات المميزات */
.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.bg-light {
    background-color: var(--light-color) !important;
    color: var(--text-color);
}

.table {
    color: var(--text-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe);
}

.table-bordered, .table-bordered th, .table-bordered td {
    border-color: var(--table-border);
}

.form-control, .form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
    border-radius: 8px;
    padding: 0.6rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    color: var(--text-color);
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
}

/* تنسيقات الأزرار */
.btn-primary {
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-border);
    color: white;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active, .btn-primary:focus {
    background-color: var(--btn-primary-active);
    border-color: var(--btn-primary-active);
    box-shadow: var(--input-focus-shadow);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.input-group-text {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--card-border);
}

.dropdown-item {
    color: var(--text-color);
}

.dropdown-item:hover {
    background-color: var(--dropdown-hover);
}

.alert-success {
    background-color: var(--alert-success-bg);
    color: var(--text-color);
}

.alert-danger {
    background-color: var(--alert-danger-bg);
    color: var(--text-color);
}

.alert-info {
    background-color: var(--alert-info-bg);
    color: var(--text-color);
}

.alert-warning {
    background-color: var(--alert-warning-bg);
    color: var(--text-color);
}

.bg-light {
    background-color: var(--light-color) !important;
    color: var(--text-color) !important;
}

.bg-dark {
    background-color: var(--dark-color) !important;
    color: var(--footer-color) !important;
}

.text-muted {
    color: var(--secondary-color) !important;
}

/* إصلاح ألوان النصوص في الوضع الليلي */
[data-theme="dark"] .text-dark {
    color: var(--text-color) !important;
}

[data-theme="dark"] .lead {
    color: var(--text-color) !important;
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: var(--text-color) !important;
}

/* زر تبديل الوضع الليلي/النهاري */
.theme-toggle {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px var(--shadow-color);
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1.2rem;
}

/* زر تبديل الوضع الليلي/النهاري في شريط التنقل */
.navbar-theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    background-color: transparent;
    border: none;
    color: var(--navbar-color);
    transition: all 0.3s ease;
    z-index: 1001;
}

.navbar-theme-toggle:hover {
    transform: scale(1.1);
    color: var(--primary-color);
}

.navbar-theme-toggle i {
    font-size: 1.5rem;
}

/* تنسيقات إضافية للتدرجات اللونية */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.gradient-bg {
    background: var(--primary-gradient);
    color: white;
}

.gradient-border {
    position: relative;
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

.gradient-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    padding: 2px;
    background: var(--primary-gradient);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
}

/* تأثيرات الحركة */
.hover-float {
    transition: transform 0.3s ease;
}

.hover-float:hover {
    transform: translateY(-5px);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* تنسيقات الظلال */
.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
    box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.shadow-hover {
    transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: var(--shadow-lg) !important;
}

/* تنسيقات الحدود */
.border-primary {
    border-color: var(--primary-color) !important;
}

.border-bottom-primary {
    border-bottom: 3px solid var(--primary-color) !important;
}

.border-gradient {
    border: double 4px transparent;
    border-radius: 8px;
    background-image: linear-gradient(white, white), var(--primary-gradient);
    background-origin: border-box;
    background-clip: content-box, border-box;
}

[data-theme="dark"] .border-gradient {
    background-image: linear-gradient(var(--card-bg), var(--card-bg)), var(--primary-gradient);
}

/* تحسينات إضافية للوضع الليلي - معالجة المشكلات المحددة */

/* إصلاح مشكلة الخلفية البيضاء والنص الأبيض */
[data-theme="dark"] section,
[data-theme="dark"] .section {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفيات في الصفحات المختلفة */
[data-theme="dark"] .page-content,
[data-theme="dark"] .content-wrapper,
[data-theme="dark"] .main-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة الفريق */
[data-theme="dark"] .team-page,
[data-theme="dark"] .team-container,
[data-theme="dark"] .team-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة المنتجات */
[data-theme="dark"] .products-page,
[data-theme="dark"] .product-container,
[data-theme="dark"] .product-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة التفاصيل */
[data-theme="dark"] .details-page,
[data-theme="dark"] .details-container,
[data-theme="dark"] .details-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة الاتصال */
[data-theme="dark"] .contact-page,
[data-theme="dark"] .contact-container,
[data-theme="dark"] .contact-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في لوحة الإدارة */
[data-theme="dark"] .admin-page,
[data-theme="dark"] .admin-container,
[data-theme="dark"] .admin-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في الصفحة الرئيسية */
[data-theme="dark"] .home-page,
[data-theme="dark"] .home-container,
[data-theme="dark"] .home-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة البكجات */
[data-theme="dark"] .packages-page,
[data-theme="dark"] .package-container,
[data-theme="dark"] .package-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في صفحة الشهادات */
[data-theme="dark"] .certificates-page,
[data-theme="dark"] .certificate-container,
[data-theme="dark"] .certificate-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في أي صفحة أخرى */
[data-theme="dark"] .page,
[data-theme="dark"] .page-container,
[data-theme="dark"] .page-content {
    background-color: var(--body-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة الخلفية في أي عنصر آخر قد يكون له خلفية بيضاء */
[data-theme="dark"] [class*="bg-white"],
[data-theme="dark"] [class*="bg-light"],
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: #ffffff"],
[data-theme="dark"] [style*="background: white"],
[data-theme="dark"] [style*="background: #fff"],
[data-theme="dark"] [style*="background: #ffffff"] {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

/* إصلاح مشكلة لون النص في أي عنصر آخر قد يكون له نص أسود */
[data-theme="dark"] [class*="text-dark"],
[data-theme="dark"] [style*="color: black"],
[data-theme="dark"] [style*="color: #000"],
[data-theme="dark"] [style*="color: #000000"] {
    color: var(--text-color) !important;
}

/* إصلاح مشكلة لون النص في أي عنصر آخر قد يكون له نص أبيض على خلفية بيضاء */
[data-theme="dark"] [class*="text-white"],
[data-theme="dark"] [style*="color: white"],
[data-theme="dark"] [style*="color: #fff"],
[data-theme="dark"] [style*="color: #ffffff"] {
    color: var(--text-color) !important;
}

/* استثناء العناصر التي يجب أن تبقى بلون أبيض مثل الأزرار والتذييل */
[data-theme="dark"] .btn-primary [class*="text-white"],
[data-theme="dark"] .btn-success [class*="text-white"],
[data-theme="dark"] .btn-info [class*="text-white"],
[data-theme="dark"] .btn-warning [class*="text-white"],
[data-theme="dark"] .btn-danger [class*="text-white"],
[data-theme="dark"] .gradient-bg [class*="text-white"],
[data-theme="dark"] footer [class*="text-white"],
[data-theme="dark"] .navbar [class*="text-white"] {
    color: white !important;
}

/* تحسينات إضافية للوضع الليلي - معالجة مشكلات محددة */

/* تحسين ظهور البطاقات في الوضع الليلي */
[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .card-header {
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom-color: var(--card-border);
}

[data-theme="dark"] .card-footer {
    background-color: rgba(255, 255, 255, 0.05);
    border-top-color: var(--card-border);
}

/* تحسين ظهور الجداول في الوضع الليلي */
[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table th {
    border-color: var(--table-border);
}

[data-theme="dark"] .table td {
    border-color: var(--table-border);
}

[data-theme="dark"] .table thead th {
    border-bottom-color: var(--table-border);
}

/* تحسين ظهور النماذج في الوضع الليلي */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
}

[data-theme="dark"] .form-control:disabled,
[data-theme="dark"] .form-select:disabled {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-muted);
}

/* تحسين ظهور الشارات في الوضع الليلي */
[data-theme="dark"] .badge {
    color: white;
}

[data-theme="dark"] .badge-light {
    background-color: var(--dark-color);
    color: var(--text-color);
}

/* تحسين ظهور التنبيهات في الوضع الليلي */
[data-theme="dark"] .alert {
    border-color: transparent;
}

[data-theme="dark"] .alert-light {
    background-color: var(--dark-color);
    color: var(--text-color);
}

/* تحسين ظهور الصور في الوضع الليلي */
[data-theme="dark"] img {
    filter: brightness(0.9);
}

/* تحسين ظهور الأيقونات في الوضع الليلي */
[data-theme="dark"] .icon {
    color: var(--primary-color);
}

/* تحسين ظهور الروابط في الوضع الليلي */
[data-theme="dark"] a {
    color: var(--primary-light);
}

[data-theme="dark"] a:hover {
    color: var(--primary-color);
}

/* تحسين ظهور الأزرار في الوضع الليلي */
[data-theme="dark"] .btn {
    color: var(--text-color);
}

[data-theme="dark"] .btn-primary,
[data-theme="dark"] .btn-success,
[data-theme="dark"] .btn-info,
[data-theme="dark"] .btn-warning,
[data-theme="dark"] .btn-danger {
    color: white;
}

/* تحسين ظهور القوائم في الوضع الليلي */
[data-theme="dark"] .list-group-item {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    color: var(--text-color);
}

[data-theme="dark"] .list-group-item-action:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .list-group-item-action:active {
    background-color: rgba(255, 255, 255, 0.1);
}

/* تحسين ظهور التبويبات في الوضع الليلي */
[data-theme="dark"] .nav-tabs {
    border-bottom-color: var(--card-border);
}

[data-theme="dark"] .nav-tabs .nav-link {
    color: var(--text-color);
}

[data-theme="dark"] .nav-tabs .nav-link:hover {
    border-color: var(--card-border) var(--card-border) transparent;
}

[data-theme="dark"] .nav-tabs .nav-link.active {
    background-color: var(--card-bg);
    border-color: var(--card-border) var(--card-border) transparent;
    color: var(--primary-color);
}

/* تحسين ظهور الشرائح في الوضع الليلي */
[data-theme="dark"] .carousel-caption {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    padding: 15px;
}

/* تحسين ظهور الأكورديون في الوضع الليلي */
[data-theme="dark"] .accordion-button {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .accordion-button:not(.collapsed) {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--primary-color);
}

[data-theme="dark"] .accordion-button::after {
    filter: invert(1);
}

/* تحسين ظهور الشريط الجانبي في الوضع الليلي */
[data-theme="dark"] .sidebar {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

/* تحسين ظهور التذييل في الوضع الليلي */
[data-theme="dark"] footer {
    background-color: var(--footer-bg);
    color: var(--footer-color);
}

/* تحسين ظهور الخط في الوضع الليلي */
[data-theme="dark"] {
    font-weight: 400;
}

/* تحسينات خاصة لصفحات محددة في الوضع الليلي */

/* صفحة الفريق - تحسينات شاملة */
[data-theme="dark"] .team-member-card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .member-info {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .member-info h4,
[data-theme="dark"] .member-info .position,
[data-theme="dark"] .member-quote {
    color: var(--text-color);
}

/* تحسينات النوافذ المنبثقة في صفحة الفريق */
[data-theme="dark"] .custom-modal-dialog {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .custom-modal-header {
    background: var(--primary-gradient) !important;
    color: white !important;
}

[data-theme="dark"] .custom-modal-body {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .custom-modal-footer {
    background-color: var(--card-bg) !important;
    border-top-color: var(--card-border) !important;
}

/* تحسينات العناصر الداخلية في النوافذ المنبثقة */
[data-theme="dark"] .custom-modal-body [style*="background-color: #f8f9fa"],
[data-theme="dark"] .custom-modal-body [style*="background-color:#f8f9fa"] {
    background-color: var(--dark-color) !important;
}

[data-theme="dark"] .custom-modal-body h5[style*="color: #007bff"],
[data-theme="dark"] .custom-modal-body h5[style*="color:#007bff"] {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body p {
    color: var(--text-color) !important;
}

[data-theme="dark"] .custom-modal-body .text-primary {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body blockquote {
    background-color: var(--card-bg) !important;
    border-color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body .btn-close-custom {
    background-color: rgba(0, 0, 0, 0.5) !important;
    color: white !important;
}

/* تحسينات إضافية للنوافذ المنبثقة في صفحة الفريق */
[data-theme="dark"] .custom-modal {
    background-color: rgba(0, 0, 0, 0.9) !important;
}

[data-theme="dark"] .custom-modal-body [style*="background-color: white"],
[data-theme="dark"] .custom-modal-body [style*="background-color:#ffffff"],
[data-theme="dark"] .custom-modal-body [style*="background-color: #fff"] {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .custom-modal-body [style*="border-right: 3px solid #007bff"],
[data-theme="dark"] .custom-modal-body [style*="border-right:3px solid #007bff"] {
    border-right-color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body [style*="border-bottom: 2px solid #007bff"],
[data-theme="dark"] .custom-modal-body [style*="border-bottom:2px solid #007bff"] {
    border-bottom-color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body .badge.bg-primary {
    background-color: var(--primary-color) !important;
}

[data-theme="dark"] .custom-modal-body .progress {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .custom-modal-body .progress-bar {
    background-color: var(--primary-color) !important;
}

/* تحسينات قسم الإنجازات */
[data-theme="dark"] .achievement-card {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .achievement-content h4 {
    color: var(--text-color);
}

[data-theme="dark"] .achievement-value {
    color: var(--primary-color);
}

/* تحسينات قسم الشهادات */
[data-theme="dark"] .certificate-card {
    background-color: var(--card-bg) !important;
    border-color: var(--card-border) !important;
}

[data-theme="dark"] .certificate-image {
    background-color: var(--dark-color) !important;
}

[data-theme="dark"] .certificate-title h5 {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .certificate-desc {
    background-color: var(--dark-color) !important;
    color: var(--text-color) !important;
    border-right-color: var(--primary-color) !important;
}

/* تحسينات النوافذ المنبثقة في قسم الشهادات */
[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .modal-header {
    background: var(--primary-gradient) !important;
    color: white;
    border-bottom-color: var(--card-border);
}

[data-theme="dark"] .modal-footer {
    background-color: var(--card-bg);
    border-top-color: var(--card-border);
}

[data-theme="dark"] .modal-body {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .modal-body [style*="background-color: #f8f9fa"] {
    background-color: var(--dark-color) !important;
}

[data-theme="dark"] .modal-body h5[style*="color: #007bff"] {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .modal-body [style*="border-right: 3px solid #007bff"],
[data-theme="dark"] .modal-body [style*="border-bottom: 2px solid #007bff"] {
    border-color: var(--primary-color) !important;
}

[data-theme="dark"] .btn-close-white {
    filter: invert(1);
}

/* تحسينات قسم الأسئلة الشائعة */
[data-theme="dark"] .accordion-item {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .accordion-button {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .accordion-button:not(.collapsed) {
    background-color: var(--primary-transparent);
    color: var(--primary-color);
}

[data-theme="dark"] .accordion-body {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* صفحة المنتجات */
[data-theme="dark"] .product-image-container {
    background-color: var(--card-bg);
}

[data-theme="dark"] .product-badge {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .product-price .price {
    color: var(--primary-light);
}

/* صفحة البكجات */
[data-theme="dark"] .package-card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .package-card .price {
    color: var(--primary-light);
}

/* صفحة الشهادات */
[data-theme="dark"] .certificate-header {
    background: var(--primary-gradient);
}

[data-theme="dark"] .certificate-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* صفحة الاتصال */
[data-theme="dark"] .contact-info {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .contact-info i {
    color: var(--primary-color);
}

/* لوحة الإدارة */
[data-theme="dark"] .admin-sidebar {
    background-color: var(--admin-sidebar-bg);
    color: var(--admin-sidebar-color);
}

[data-theme="dark"] .admin-sidebar .nav-link {
    color: var(--admin-sidebar-color);
}

[data-theme="dark"] .admin-sidebar .nav-link:hover,
[data-theme="dark"] .admin-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--primary-light);
}

[data-theme="dark"] .admin-card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .admin-card .card-header {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

/* تحسينات إضافية للتأكد من عدم وجود نصوص بيضاء على خلفية بيضاء */
[data-theme="dark"] * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* تحسينات نهائية لصفحة فريق العمل */
[data-theme="dark"] .team-section {
    background-color: var(--body-bg) !important;
}

[data-theme="dark"] .team-achievements {
    background-color: var(--body-bg) !important;
}

[data-theme="dark"] .gallery-section {
    background-color: var(--body-bg) !important;
}

[data-theme="dark"] .certificates-section {
    background-color: var(--body-bg) !important;
}

/* تحسينات خاصة للعناوين في قسم الإنجازات */
[data-theme="dark"] .section-title .title,
[data-theme="dark"] .section-title h2.title {
    color: var(--text-color) !important;
}

[data-theme="dark"] .section-title .section-description,
[data-theme="dark"] .section-title p.section-description {
    color: var(--text-color) !important;
}

[data-theme="dark"] .team-intro h3 {
    color: var(--text-color) !important;
}

[data-theme="dark"] .team-intro .lead {
    color: var(--text-color) !important;
}

[data-theme="dark"] .team-intro p {
    color: var(--text-color) !important;
}

/* تحسينات إضافية للنصوص في صفحة فريق العمل */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6,
[data-theme="dark"] p {
    color: var(--text-color) !important;
}

/* تحسينات خاصة للعناوين في قسم الإنجازات */
[data-theme="dark"] .team-achievements .section-title .title,
[data-theme="dark"] .team-achievements .section-title h2.title,
[data-theme="dark"] .team-achievements h2,
[data-theme="dark"] .team-achievements h2.title {
    color: var(--text-color) !important;
}

[data-theme="dark"] .team-achievements .section-title .section-description,
[data-theme="dark"] .team-achievements .section-title p.section-description,
[data-theme="dark"] .team-achievements p,
[data-theme="dark"] .team-achievements .section-description {
    color: var(--text-color) !important;
}

/* تحسينات خاصة للعنوان "فريق متكامل من الخبراء" */
[data-theme="dark"] .team-intro h3,
[data-theme="dark"] h3.text-dark,
[data-theme="dark"] h3.text-black,
[data-theme="dark"] .row h3,
[data-theme="dark"] .col-md-6 h3 {
    color: var(--text-color) !important;
}

/* تحسينات إضافية لقسم الإنجازات */
[data-theme="dark"] .team-achievements .row .col-lg-7,
[data-theme="dark"] .team-achievements .row .col-lg-4,
[data-theme="dark"] .team-achievements .row .col-md-6 {
    color: var(--text-color) !important;
}

[data-theme="dark"] .team-achievements .row .col-lg-7 *,
[data-theme="dark"] .team-achievements .row .col-lg-4 *,
[data-theme="dark"] .team-achievements .row .col-md-6 * {
    color: var(--text-color) !important;
}

/* تحسينات خاصة للعناوين في قسم الإنجازات */
[data-theme="dark"] .team-achievements .subtitle,
[data-theme="dark"] .team-achievements span.subtitle {
    color: var(--primary-color) !important;
    background-color: var(--primary-transparent) !important;
}

/* تحسينات خاصة للعنوان "إنجازاتنا خبرة وكفاءة" */
[data-theme="dark"] .team-achievements .title,
[data-theme="dark"] .team-achievements h2.title {
    color: var(--text-color) !important;
}

/* تحسينات نهائية لجميع النصوص في صفحة فريق العمل */
[data-theme="dark"] .team-section *,
[data-theme="dark"] .team-achievements *,
[data-theme="dark"] .gallery-section *,
[data-theme="dark"] .certificates-section * {
    color: var(--text-color);
}

/* استثناءات للألوان الخاصة */
[data-theme="dark"] .team-section .subtitle,
[data-theme="dark"] .team-achievements .subtitle,
[data-theme="dark"] .gallery-section .subtitle,
[data-theme="dark"] .certificates-section .subtitle {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .team-section .btn,
[data-theme="dark"] .team-achievements .btn,
[data-theme="dark"] .gallery-section .btn,
[data-theme="dark"] .certificates-section .btn {
    color: inherit;
}

[data-theme="dark"] .team-section .btn-primary,
[data-theme="dark"] .team-achievements .btn-primary,
[data-theme="dark"] .gallery-section .btn-primary,
[data-theme="dark"] .certificates-section .btn-primary {
    color: white !important;
}

[data-theme="dark"] .team-section .btn-outline-primary,
[data-theme="dark"] .team-achievements .btn-outline-primary,
[data-theme="dark"] .gallery-section .btn-outline-primary,
[data-theme="dark"] .certificates-section .btn-outline-primary {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .team-section .btn-outline-primary:hover,
[data-theme="dark"] .team-achievements .btn-outline-primary:hover,
[data-theme="dark"] .gallery-section .btn-outline-primary:hover,
[data-theme="dark"] .certificates-section .btn-outline-primary:hover {
    color: white !important;
}

/* تحسينات خاصة للعناصر المضمنة في صفحة فريق العمل */
[data-theme="dark"] [style*="color: white"],
[data-theme="dark"] [style*="color:#ffffff"],
[data-theme="dark"] [style*="color: #fff"] {
    color: var(--text-color) !important;
}

/* استثناءات للعناصر التي يجب أن تبقى بيضاء */
[data-theme="dark"] .custom-modal-header [style*="color: white"],
[data-theme="dark"] .custom-modal-header [style*="color:#ffffff"],
[data-theme="dark"] .custom-modal-header [style*="color: #fff"],
[data-theme="dark"] .btn-primary [style*="color: white"],
[data-theme="dark"] .btn-primary [style*="color:#ffffff"],
[data-theme="dark"] .btn-primary [style*="color: #fff"] {
    color: white !important;
}

[data-theme="dark"] .gallery-filter .btn {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--card-border);
}

[data-theme="dark"] .gallery-filter .btn.active,
[data-theme="dark"] .gallery-filter .btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

[data-theme="dark"] .gallery-caption {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .gallery-caption h5 {
    color: var(--text-color);
}

[data-theme="dark"] .gallery-meta {
    color: var(--text-muted);
}

[data-theme="dark"] .gallery-meta i {
    color: var(--primary-color);
}

/* تحسينات إضافية للنوافذ المنبثقة المخصصة */
[data-theme="dark"] div[style*="background-color: white"] {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] div[style*="background-color: rgba(0,0,0,0.8)"] {
    background-color: rgba(0, 0, 0, 0.9) !important;
}

/* تحسينات للعناصر المضمنة في صفحة فريق العمل */
[data-theme="dark"] .display-4.fw-bold.text-primary {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .section-title .subtitle {
    color: var(--primary-color);
    background-color: var(--primary-transparent);
}

[data-theme="dark"] .section-title .title {
    color: var(--text-color) !important;
}

[data-theme="dark"] .section-description {
    color: var(--text-color) !important;
}

/* تحسينات للصور في الوضع الليلي */
[data-theme="dark"] .img-fluid {
    filter: brightness(0.85);
}

[data-theme="dark"] .rounded-circle {
    border-color: var(--card-border) !important;
}

/* تحسينات نهائية للوضع الليلي */

/* تحسين ظهور الخلفيات المخصصة */
[data-theme="dark"] [style*="background-color"] {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

/* استثناء العناصر التي يجب أن تحتفظ بألوانها */
[data-theme="dark"] .btn[style*="background-color"],
[data-theme="dark"] .badge[style*="background-color"],
[data-theme="dark"] .alert[style*="background-color"],
[data-theme="dark"] .progress-bar[style*="background-color"],
[data-theme="dark"] .feature-icon-wrapper[style*="background-color"],
[data-theme="dark"] .gradient-bg[style*="background-color"] {
    background-color: inherit !important;
}

/* تحسين ظهور النصوص المخصصة */
[data-theme="dark"] [style*="color"] {
    color: var(--text-color) !important;
}

/* استثناء العناصر التي يجب أن تحتفظ بألوان نصوصها */
[data-theme="dark"] .btn[style*="color"],
[data-theme="dark"] .badge[style*="color"],
[data-theme="dark"] .alert[style*="color"],
[data-theme="dark"] .feature-icon-wrapper[style*="color"],
[data-theme="dark"] .gradient-text[style*="color"],
[data-theme="dark"] a[style*="color"] {
    color: inherit !important;
}

/* تحسين ظهور الحدود المخصصة */
[data-theme="dark"] [style*="border-color"] {
    border-color: var(--card-border) !important;
}

/* استثناء العناصر التي يجب أن تحتفظ بألوان حدودها */
[data-theme="dark"] .btn[style*="border-color"],
[data-theme="dark"] .form-control:focus[style*="border-color"],
[data-theme="dark"] .form-select:focus[style*="border-color"] {
    border-color: inherit !important;
}

/* تحسين ظهور الظلال المخصصة */
[data-theme="dark"] [style*="box-shadow"] {
    box-shadow: var(--shadow-md) !important;
}

/* استثناء العناصر التي يجب أن تحتفظ بظلالها */
[data-theme="dark"] .btn:hover[style*="box-shadow"],
[data-theme="dark"] .card:hover[style*="box-shadow"],
[data-theme="dark"] .form-control:focus[style*="box-shadow"],
[data-theme="dark"] .form-select:focus[style*="box-shadow"] {
    box-shadow: inherit !important;
}

/* تحسين ظهور الصور في الوضع الليلي */
[data-theme="dark"] img:not(.no-dark-filter) {
    filter: brightness(0.9) contrast(1.1);
}

/* تحسين ظهور الأيقونات في الوضع الليلي */
[data-theme="dark"] .fa,
[data-theme="dark"] .fas,
[data-theme="dark"] .far,
[data-theme="dark"] .fab {
    color: inherit;
}

/* تحسين ظهور الأيقونات المخصصة في الوضع الليلي */
[data-theme="dark"] i[style*="color"] {
    color: inherit !important;
}

/* تحسين ظهور الخلفيات الشفافة في الوضع الليلي */
[data-theme="dark"] [style*="background: transparent"],
[data-theme="dark"] [style*="background-color: transparent"] {
    background-color: transparent !important;
}

/* تحسين ظهور الخلفيات المتدرجة في الوضع الليلي */
[data-theme="dark"] [style*="background: linear-gradient"],
[data-theme="dark"] [style*="background-image: linear-gradient"] {
    background: var(--primary-gradient) !important;
}

/* ===== أنماط لوحة التحكم المحسنة ===== */

/* الشريط الجانبي الجميل */
.admin-sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* تأثير الخلفية المتحركة */
.admin-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0.3;
    animation: moveBackground 20s linear infinite;
}

@keyframes moveBackground {
    0% { transform: translateX(0); }
    100% { transform: translateX(20px); }
}

/* محتوى الشريط الجانبي */
.admin-sidebar .d-flex {
    position: relative;
    z-index: 2;
}

/* شعار لوحة التحكم */
.admin-sidebar .fs-4 {
    font-weight: 700;
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* روابط التنقل المحسنة */
.admin-sidebar .nav-link {
    color: #ffffff !important;
    padding: 15px 20px;
    margin: 5px 10px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    backdrop-filter: blur(10px);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* تأثير الخلفية عند التمرير */
.admin-sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.admin-sidebar .nav-link:hover::before {
    left: 100%;
}

/* حالة التمرير والنشط */
.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    transform: translateX(5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
}

.admin-sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2) !important;
    border-left: 4px solid #fff;
}

/* أيقونات التنقل */
.admin-sidebar .nav-link i {
    margin-left: 12px;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.admin-sidebar .nav-link:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* خط الفاصل */
.admin-sidebar hr {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 20px 15px;
}

/* قائمة المستخدم المنسدلة */
.admin-sidebar .dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.admin-sidebar .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* المحتوى الرئيسي */
.admin-content {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    position: relative;
}

/* تأثير الخلفية للمحتوى */
.admin-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* البطاقات المحسنة */
.admin-card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.admin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 300% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* رؤوس البطاقات */
.admin-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 25px 30px 20px;
    position: relative;
}

.admin-card .card-header h5 {
    font-weight: 600;
    color: #1a202c !important;
    margin: 0;
    font-size: 1.25rem;
    text-shadow: none;
}

/* محتوى البطاقات */
.admin-card .card-body {
    padding: 25px 30px 30px;
}

/* بطاقات الإحصائيات */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20px;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.5s ease;
}

.stat-card:hover::before {
    transform: scale(1);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(102, 126, 234, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: rotate(360deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 10px 0 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-title {
    font-size: 0.95rem;
    opacity: 0.9;
    font-weight: 500;
}

/* الأزرار المحسنة */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 25px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* الجداول المحسنة */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px 15px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: scale(1.01);
}

.table tbody td {
    padding: 18px 15px;
    border-color: rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

/* أدوات الإدارة */
.tools-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.tool-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tool-card:hover::before {
    opacity: 1;
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.tool-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 32px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.tool-card:hover .tool-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.tool-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1a202c !important;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

.tool-description {
    color: #4a5568 !important;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
}

/* التنبيهات المحسنة */
.alert {
    border: none;
    border-radius: 15px;
    padding: 20px 25px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 178, 172, 0.1));
    color: #2f855a;
    border-left: 4px solid #48bb78;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
    color: #c53030;
    border-left: 4px solid #f56565;
}

.alert-info {
    background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(49, 130, 206, 0.1));
    color: #2c5282;
    border-left: 4px solid #4299e1;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(236, 201, 75, 0.1), rgba(214, 158, 46, 0.1));
    color: #b7791f;
    border-left: 4px solid #ecc94b;
}

/* الوضع الليلي للوحة التحكم */
[data-theme="dark"] .admin-sidebar {
    background: linear-gradient(135deg, #c86808 0%, #e87a09 100%);
}

[data-theme="dark"] .admin-content {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

[data-theme="dark"] .admin-card {
    background: rgba(45, 55, 72, 0.95);
    color: #e2e8f0 !important;
}

[data-theme="dark"] .admin-card .card-header h5 {
    color: #f7fafc !important;
    text-shadow: none;
}

[data-theme="dark"] .tool-card {
    background: rgba(45, 55, 72, 0.95);
    color: #e2e8f0 !important;
}

[data-theme="dark"] .tool-title {
    color: #f7fafc !important;
}

[data-theme="dark"] .tool-description {
    color: #cbd5e0 !important;
}

/* تحسين النصوص في الوضع الليلي */
[data-theme="dark"] .admin-sidebar .nav-link {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .admin-sidebar .fs-4 {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] .admin-sidebar .dropdown-toggle {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .table tbody tr:hover {
    background: linear-gradient(90deg, rgba(200, 104, 8, 0.1), rgba(232, 122, 9, 0.1));
}

/* تحسينات إضافية للنصوص */
.admin-content h1, .admin-content h2, .admin-content h3,
.admin-content h4, .admin-content h5, .admin-content h6 {
    color: #1a202c !important;
    font-weight: 600;
}

[data-theme="dark"] .admin-content h1,
[data-theme="dark"] .admin-content h2,
[data-theme="dark"] .admin-content h3,
[data-theme="dark"] .admin-content h4,
[data-theme="dark"] .admin-content h5,
[data-theme="dark"] .admin-content h6 {
    color: #f7fafc !important;
}

/* تحسين النصوص في الجداول */
.table td, .table th {
    color: #2d3748 !important;
}

[data-theme="dark"] .table td {
    color: #e2e8f0 !important;
}

[data-theme="dark"] .table th {
    color: #ffffff !important;
}

/* تحسين النصوص في النماذج */
.form-label {
    color: #2d3748 !important;
    font-weight: 500;
}

[data-theme="dark"] .form-label {
    color: #e2e8f0 !important;
}

/* تحسين النصوص في التنبيهات */
.alert {
    font-weight: 500;
}

/* تحسين الأزرار الثانوية */
.btn-outline-primary {
    border-color: #c86808;
    color: #c86808;
}

.btn-outline-primary:hover {
    background-color: #c86808;
    border-color: #c86808;
    color: #ffffff;
}

/* تحسينات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* تحسين شريط التمرير */
.admin-content::-webkit-scrollbar {
    width: 8px;
}

.admin-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.admin-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
}

.admin-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
